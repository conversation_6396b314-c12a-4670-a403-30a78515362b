class AppConstants {
  // 数据库相关
  static const String databaseName = 'chatbot.db';
  static const int databaseVersion = 1;

  // 默认设置
  static const String defaultModelId = 'gpt-4';
  static const String defaultVisibility = 'private';
  static const String defaultMessageType = 'text';

  // 分页相关
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // 文件上传限制
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const List<String> imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'bmp'
  ];
  static const List<String> documentExtensions = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'txt',
    'md',
    'json',
    'csv'
  ];
  static const List<String> supportedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
  static const List<String> supportedDocumentTypes = [
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  // UI相关
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  static const double padding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // 动画时长
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // API相关（占位符）
  static const String apiBaseUrl = 'https://api.example.com';
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 10);

  // 消息角色
  static const String userRole = 'user';
  static const String assistantRole = 'assistant';
  static const String systemRole = 'system';

  // 聊天可见性
  static const String privateVisibility = 'private';
  static const String publicVisibility = 'public';

  // 文档类型
  static const String textDocumentKind = 'text';
  static const String codeDocumentKind = 'code';
  static const String imageDocumentKind = 'image';
  static const String sheetDocumentKind = 'sheet';

  // 错误消息
  static const String networkError = '网络连接失败，请检查网络设置';
  static const String databaseError = '数据库操作失败';
  static const String fileUploadError = '文件上传失败';
  static const String fileSizeError = '文件大小超过限制';
  static const String fileTypeError = '不支持的文件类型';
  static const String unknownError = '发生未知错误';

  // 成功消息
  static const String chatCreatedSuccess = '聊天创建成功';
  static const String chatDeletedSuccess = '聊天删除成功';
  static const String messageDeletedSuccess = '消息删除成功';
  static const String documentSavedSuccess = '文档保存成功';
  static const String settingsSavedSuccess = '设置保存成功';

  // 确认消息
  static const String deleteChatConfirm = '确定要删除这个聊天吗？此操作无法撤销。';
  static const String deleteMessageConfirm = '确定要删除这条消息吗？';
  static const String deleteDocumentConfirm = '确定要删除这个文档吗？';
  static const String clearAllDataConfirm = '确定要清空所有数据吗？此操作无法撤销。';

  // 设置键
  static const String settingThemeMode = 'theme_mode';
  static const String settingSelectedModel = 'selected_model';
  static const String settingLanguage = 'language';
  static const String settingAutoSave = 'auto_save';
  static const String settingNotifications = 'notifications';

  // 支持的AI模型
  static const List<Map<String, String>> supportedModels = [
    {'id': 'gpt-4', 'name': 'GPT-4', 'description': '最先进的对话AI模型'},
    {
      'id': 'gpt-3.5-turbo',
      'name': 'GPT-3.5 Turbo',
      'description': '快速响应的对话模型',
    },
    {
      'id': 'claude-3-opus',
      'name': 'Claude 3 Opus',
      'description': 'Anthropic的高性能模型',
    },
    {
      'id': 'claude-3-sonnet',
      'name': 'Claude 3 Sonnet',
      'description': '平衡性能和速度',
    },
  ];

  // 预设建议操作
  static const List<String> suggestedActions = [
    '解释这段代码',
    '帮我写一个函数',
    '优化这个算法',
    '查找代码中的bug',
    '添加注释',
    '重构代码结构',
    '生成测试用例',
    '翻译成其他语言',
  ];

  // 编程语言支持
  static const List<Map<String, String>> supportedLanguages = [
    {'id': 'javascript', 'name': 'JavaScript', 'extension': 'js'},
    {'id': 'typescript', 'name': 'TypeScript', 'extension': 'ts'},
    {'id': 'python', 'name': 'Python', 'extension': 'py'},
    {'id': 'java', 'name': 'Java', 'extension': 'java'},
    {'id': 'cpp', 'name': 'C++', 'extension': 'cpp'},
    {'id': 'csharp', 'name': 'C#', 'extension': 'cs'},
    {'id': 'go', 'name': 'Go', 'extension': 'go'},
    {'id': 'rust', 'name': 'Rust', 'extension': 'rs'},
    {'id': 'php', 'name': 'PHP', 'extension': 'php'},
    {'id': 'ruby', 'name': 'Ruby', 'extension': 'rb'},
    {'id': 'swift', 'name': 'Swift', 'extension': 'swift'},
    {'id': 'kotlin', 'name': 'Kotlin', 'extension': 'kt'},
    {'id': 'dart', 'name': 'Dart', 'extension': 'dart'},
    {'id': 'html', 'name': 'HTML', 'extension': 'html'},
    {'id': 'css', 'name': 'CSS', 'extension': 'css'},
    {'id': 'sql', 'name': 'SQL', 'extension': 'sql'},
    {'id': 'json', 'name': 'JSON', 'extension': 'json'},
    {'id': 'yaml', 'name': 'YAML', 'extension': 'yaml'},
    {'id': 'markdown', 'name': 'Markdown', 'extension': 'md'},
  ];

  // 禁止实例化
  AppConstants._();
}
