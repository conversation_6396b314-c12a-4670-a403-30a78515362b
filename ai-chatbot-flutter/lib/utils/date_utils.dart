import 'package:intl/intl.dart';

class DateUtils {
  static String groupChatByDate(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final lastWeek = today.subtract(const Duration(days: 7));
    final lastMonth = today.subtract(const Duration(days: 30));

    if (timestamp.isAfter(today)) {
      return 'Today';
    } else if (timestamp.isAfter(yesterday)) {
      return 'Yesterday';
    } else if (timestamp.isAfter(lastWeek)) {
      return 'Last 7 days';
    } else if (timestamp.isAfter(lastMonth)) {
      return 'Last 30 days';
    } else {
      return 'Older';
    }
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat('h:mm a').format(dateTime);
  }

  static String formatTimestamp(DateTime timestamp) {
    return DateFormat('MMMM d, yyyy').format(timestamp);
  }
}
