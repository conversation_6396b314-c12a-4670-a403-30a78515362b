import 'dart:math';

class IdGenerator {
  static final Random _random = Random();

  /// 生成唯一ID，格式：时间戳 + 随机字符串
  static String generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomString = _generateRandomString(8);
    return '${timestamp}_$randomString';
  }

  /// 生成短ID，用于不需要时间戳的场景
  static String generateShortId() {
    return _generateRandomString(12);
  }

  /// 生成随机字符串
  static String _generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(
        length, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// 生成聊天ID
  static String generateChatId() {
    return 'chat_${generateId()}';
  }

  /// 生成消息ID
  static String generateMessageId() {
    return 'msg_${generateId()}';
  }

  /// 生成文档ID
  static String generateDocumentId() {
    return 'doc_${generateId()}';
  }

  /// 生成建议ID
  static String generateSuggestionId() {
    return 'sug_${generateId()}';
  }

  /// 生成附件ID
  static String generateAttachmentId() {
    return 'att_${generateId()}';
  }

  /// 生成投票ID
  static String generateVoteId() {
    return 'vote_${generateId()}';
  }
}
