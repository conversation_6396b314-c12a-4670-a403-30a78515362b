import 'package:sqflite/sqflite.dart';

import '../models/vote_model.dart';
import '../services/database_service.dart';

/// VoteRepository 接口
/// 定义了所有与投票数据持久化相关的操作。
abstract class IVoteRepository {
  /// 更新或插入一条投票记录。
  Future<void> upsertVote(String messageId, bool isUpvoted);

  /// 根据聊天ID获取该聊天中所有的投票。
  Future<Map<String, VoteModel>> getVotesByChatId(String chatId);

  /// 根据消息ID获取单条投票。
  Future<VoteModel?> getVoteByMessageId(String messageId);
}

/// VoteRepository 的 SQLite 实现
class SqliteVoteRepository implements IVoteRepository {
  final DatabaseService _dbService;

  SqliteVoteRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> upsertVote(String messageId, bool isUpvoted) async {
    final db = await _db;
    final existing = await db.query(
      'votes',
      where: 'message_id = ?',
      whereArgs: [messageId],
    );

    final voteData = {
      'message_id': messageId,
      'is_upvoted': isUpvoted ? 1 : 0,
      'created_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      voteData['id'] = 'vote_${DateTime.now().millisecondsSinceEpoch}';
      await db.insert('votes', voteData);
    } else {
      await db.update(
        'votes',
        voteData,
        where: 'message_id = ?',
        whereArgs: [messageId],
      );
    }
  }

  @override
  Future<Map<String, VoteModel>> getVotesByChatId(String chatId) async {
    final db = await _db;
    final result = await db.rawQuery(
      '''
      SELECT v.* FROM votes v
      JOIN messages m ON v.message_id = m.id
      WHERE m.chat_id = ?
    ''',
      [chatId],
    );

    final votes = <String, VoteModel>{};
    for (final json in result) {
      final vote = VoteModel.fromDatabase(json);
      votes[vote.messageId] = vote;
    }
    return votes;
  }

  @override
  Future<VoteModel?> getVoteByMessageId(String messageId) async {
    final db = await _db;
    final result = await db.query(
      'votes',
      where: 'message_id = ?',
      whereArgs: [messageId],
      limit: 1,
    );
    if (result.isEmpty) return null;
    return VoteModel.fromDatabase(result.first);
  }
}
