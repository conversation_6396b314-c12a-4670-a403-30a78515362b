import 'package:sqflite/sqflite.dart';

import '../models/document_model.dart';
import '../services/database_service.dart';

/// DocumentRepository 接口
/// 定义了所有与文档 (Artifacts) 数据持久化相关的操作。
abstract class IDocumentRepository {
  /// 添加一个新文档。
  Future<void> insertDocument(DocumentModel document);

  /// 根据ID获取一个文档。
  Future<DocumentModel?> getDocumentById(String documentId);

  /// 获取所有文档的列表。
  Future<List<DocumentModel>> getAllDocuments();

  /// 更新一个已有的文档。
  Future<void> updateDocument(DocumentModel document);

  /// 根据ID删除一个文档。
  Future<void> deleteDocument(String documentId);

  /// 获取一个文档的所有版本。
  Future<List<DocumentModel>> getDocumentVersions(String documentId);
}

/// DocumentRepository 的 SQLite 实现
class SqliteDocumentRepository implements IDocumentRepository {
  final DatabaseService _dbService;

  SqliteDocumentRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> insertDocument(DocumentModel document) async {
    final db = await _db;
    await db.insert('documents', document.toDatabase());
  }

  @override
  Future<DocumentModel?> getDocumentById(String documentId) async {
    final db = await _db;
    final result = await db.query(
      'documents',
      where: 'id = ?',
      whereArgs: [documentId],
      limit: 1,
    );
    if (result.isEmpty) return null;
    return DocumentModel.fromDatabase(result.first);
  }

  @override
  Future<List<DocumentModel>> getAllDocuments() async {
    final db = await _db;
    final result = await db.query('documents', orderBy: 'updated_at DESC');
    return result.map((json) => DocumentModel.fromDatabase(json)).toList();
  }

  @override
  Future<void> updateDocument(DocumentModel document) async {
    final db = await _db;
    await db.update(
      'documents',
      document.toDatabase(),
      where: 'id = ?',
      whereArgs: [document.id],
    );
  }

  @override
  Future<void> deleteDocument(String documentId) async {
    final db = await _db;
    await db.delete('documents', where: 'id = ?', whereArgs: [documentId]);
  }

  @override
  Future<List<DocumentModel>> getDocumentVersions(String documentId) async {
    final db = await _db;
    final result = await db.query(
      'documents',
      where: 'parent_id = ? OR id = ?',
      whereArgs: [documentId, documentId],
      orderBy: 'version DESC',
    );
    return result.map((json) => DocumentModel.fromDatabase(json)).toList();
  }
}
