import 'package:sqflite/sqflite.dart';
import '../models/suggestion_model.dart';
import '../services/database_service.dart';

abstract class ISuggestionRepository {
  Future<void> addSuggestion(SuggestionModel suggestion);
  Future<List<SuggestionModel>> getSuggestionsByDocument(String documentId);
  Future<void> updateSuggestion(SuggestionModel suggestion);
  Future<void> deleteSuggestion(String suggestionId);
}

class SqliteSuggestionRepository implements ISuggestionRepository {
  final DatabaseService _dbService;

  SqliteSuggestionRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> addSuggestion(SuggestionModel suggestion) async {
    final db = await _db;
    await db.insert(
      'suggestions',
      suggestion.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<List<SuggestionModel>> getSuggestionsByDocument(
    String documentId,
  ) async {
    final db = await _db;
    final List<Map<String, dynamic>> maps = await db.query(
      'suggestions',
      where: 'document_id = ?',
      whereArgs: [documentId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) {
      return SuggestionModel.fromJson(maps[i]);
    });
  }

  @override
  Future<void> updateSuggestion(SuggestionModel suggestion) async {
    final db = await _db;
    await db.update(
      'suggestions',
      suggestion.toJson(),
      where: 'id = ?',
      whereArgs: [suggestion.id],
    );
  }

  @override
  Future<void> deleteSuggestion(String suggestionId) async {
    final db = await _db;
    await db.delete('suggestions', where: 'id = ?', whereArgs: [suggestionId]);
  }
}
