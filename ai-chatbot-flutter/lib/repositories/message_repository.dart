import 'package:sqflite/sqflite.dart';

import '../models/message_model.dart';
import '../services/database_service.dart';

/// MessageRepository 接口
/// 定义了所有与消息数据持久化相关的操作。
abstract class IMessageRepository {
  /// 添加一条新消息。
  Future<void> addMessage(MessageModel message);

  /// 根据聊天ID获取分页的消息列表。
  Future<List<MessageModel>> getMessagesByChatId(
    String chatId, {
    int? limit,
    int? offset,
  });

  /// 根据ID查找一条消息。
  Future<MessageModel?> findMessageById(String messageId);

  /// 更新一条已有的消息。
  Future<void> updateMessage(MessageModel message);

  /// 根据ID删除一条消息。
  Future<void> deleteMessage(String messageId);

  /// 根据聊天ID删除所有消息。
  Future<void> deleteMessagesByChatId(String chatId);

  /// 从某条消息开始，删除其后的所有消息。
  Future<void> deleteMessagesFrom(String startMessageId, String chatId);
}

/// MessageRepository 的 SQLite 实现
class SqliteMessageRepository implements IMessageRepository {
  final DatabaseService _dbService;

  SqliteMessageRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> addMessage(MessageModel message) async {
    final db = await _db;
    await db.insert('messages', message.toDatabase());
  }

  @override
  Future<List<MessageModel>> getMessagesByChatId(
    String chatId, {
    int? limit,
    int? offset,
  }) async {
    final db = await _db;
    final result = await db.query(
      'messages',
      where: 'chat_id = ?',
      whereArgs: [chatId],
      orderBy: 'created_at ASC',
      limit: limit,
      offset: offset,
    );
    return result.map((json) => MessageModel.fromDatabase(json)).toList();
  }

  @override
  Future<MessageModel?> findMessageById(String messageId) async {
    final db = await _db;
    final result = await db.query(
      'messages',
      where: 'id = ?',
      whereArgs: [messageId],
      limit: 1,
    );
    if (result.isEmpty) return null;
    return MessageModel.fromDatabase(result.first);
  }

  @override
  Future<void> updateMessage(MessageModel message) async {
    final db = await _db;
    await db.update(
      'messages',
      message.toDatabase(),
      where: 'id = ?',
      whereArgs: [message.id],
    );
  }

  @override
  Future<void> deleteMessage(String messageId) async {
    final db = await _db;
    await db.delete('messages', where: 'id = ?', whereArgs: [messageId]);
  }

  @override
  Future<void> deleteMessagesByChatId(String chatId) async {
    final db = await _db;
    await db.delete('messages', where: 'chat_id = ?', whereArgs: [chatId]);
  }

  @override
  Future<void> deleteMessagesFrom(String startMessageId, String chatId) async {
    final db = await _db;

    final startMessage = await findMessageById(startMessageId);
    if (startMessage == null) return;

    await db.delete(
      'messages',
      where: 'chat_id = ? AND created_at > ?',
      whereArgs: [chatId, startMessage.createdAt.toIso8601String()],
    );
  }
}
