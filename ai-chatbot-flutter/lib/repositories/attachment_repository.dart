import 'package:sqflite/sqflite.dart';

import '../models/attachment_model.dart';
import '../services/database_service.dart';

/// AttachmentRepository 接口
/// 定义了所有与附件数据持久化相关的操作。
abstract class IAttachmentRepository {
  /// 添加一个新的附件。
  Future<void> addAttachment(AttachmentModel attachment);

  /// 根据消息ID获取附件列表。
  Future<List<AttachmentModel>> getAttachmentsByMessageId(String messageId);

  /// 根据聊天ID获取该聊天中的所有附件。
  Future<List<AttachmentModel>> getAttachmentsByChatId(String chatId);

  /// 根据ID删除一个附件。
  Future<void> deleteAttachment(String attachmentId);

  /// 根据聊天ID删除所有附件。
  Future<void> deleteAttachmentsByChatId(String chatId);
}

/// AttachmentRepository 的 SQLite 实现
class SqliteAttachmentRepository implements IAttachmentRepository {
  final DatabaseService _dbService;

  SqliteAttachmentRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> addAttachment(AttachmentModel attachment) async {
    final db = await _db;
    await db.insert('attachments', attachment.toDatabase());
  }

  @override
  Future<List<AttachmentModel>> getAttachmentsByMessageId(
    String messageId,
  ) async {
    final db = await _db;
    final result = await db.query(
      'attachments',
      where: 'message_id = ?',
      whereArgs: [messageId],
      orderBy: 'created_at ASC',
    );
    return result.map((json) => AttachmentModel.fromDatabase(json)).toList();
  }

  @override
  Future<List<AttachmentModel>> getAttachmentsByChatId(String chatId) async {
    final db = await _db;
    final result = await db.rawQuery(
      '''
      SELECT a.*
      FROM attachments a
      JOIN messages m ON a.message_id = m.id
      WHERE m.chat_id = ?
    ''',
      [chatId],
    );
    return result.map((json) => AttachmentModel.fromDatabase(json)).toList();
  }

  @override
  Future<void> deleteAttachment(String attachmentId) async {
    final db = await _db;
    await db.delete('attachments', where: 'id = ?', whereArgs: [attachmentId]);
  }

  @override
  Future<void> deleteAttachmentsByChatId(String chatId) async {
    final db = await _db;
    final messageIds = await db.query(
      'messages',
      columns: ['id'],
      where: 'chat_id = ?',
      whereArgs: [chatId],
    );
    if (messageIds.isEmpty) return;

    final ids = messageIds.map((row) => row['id'] as String).toList();
    await db.delete(
      'attachments',
      where: 'message_id IN (${ids.map((_) => '?').join(',')})',
      whereArgs: ids,
    );
  }
}
