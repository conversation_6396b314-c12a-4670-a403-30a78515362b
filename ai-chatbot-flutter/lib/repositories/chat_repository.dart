import 'package:sqflite/sqflite.dart';

import '../models/chat_model.dart';
import '../services/database_service.dart';

/// ChatRepository 接口
/// 定义了所有与聊天数据持久化相关的操作。
abstract class IChatRepository {
  /// 添加一个新的聊天。
  Future<void> addChat(ChatModel chat);

  /// 获取分页的聊天列表。
  Future<List<ChatModel>> getChats({int? limit, int? offset});

  /// 根据ID查找一个聊天。
  Future<ChatModel?> findChatById(String chatId);

  /// 更新一个已有的聊天。
  Future<void> updateChat(ChatModel chat);

  /// 更新聊天的可见性。
  Future<void> updateChatVisibility(String chatId, String visibility);

  /// 根据ID删除一个聊天。
  Future<void> deleteChat(String chatId);

  /// 根据标题搜索聊天。
  Future<List<ChatModel>> searchChats(String query);
}

/// ChatRepository 的 SQLite 实现
/// 实现了 IChatRepository 接口，负责所有针对 SQLite 数据库的聊天数据操作。
class SqliteChatRepository implements IChatRepository {
  final DatabaseService _dbService;

  SqliteChatRepository(this._dbService);

  Future<Database> get _db => _dbService.database;

  @override
  Future<void> addChat(ChatModel chat) async {
    final db = await _db;
    await db.insert('chats', chat.toDatabase());
  }

  @override
  Future<List<ChatModel>> getChats({int? limit, int? offset}) async {
    final db = await _db;
    final result = await db.query(
      'chats',
      orderBy: 'updated_at DESC',
      limit: limit,
      offset: offset,
    );
    return result.map((json) => ChatModel.fromDatabase(json)).toList();
  }

  @override
  Future<ChatModel?> findChatById(String chatId) async {
    final db = await _db;
    final result = await db.query(
      'chats',
      where: 'id = ?',
      whereArgs: [chatId],
      limit: 1,
    );
    if (result.isEmpty) return null;
    return ChatModel.fromDatabase(result.first);
  }

  @override
  Future<void> updateChat(ChatModel chat) async {
    final db = await _db;
    await db.update(
      'chats',
      chat.toDatabase(),
      where: 'id = ?',
      whereArgs: [chat.id],
    );
  }

  @override
  Future<void> updateChatVisibility(String chatId, String visibility) async {
    final db = await _db;
    await db.update(
      'chats',
      {
        'visibility': visibility,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [chatId],
    );
  }

  @override
  Future<void> deleteChat(String chatId) async {
    final db = await _db;
    await db.delete('chats', where: 'id = ?', whereArgs: [chatId]);
  }

  @override
  Future<List<ChatModel>> searchChats(String query) async {
    final db = await _db;
    final result = await db.query(
      'chats',
      where: 'title LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'updated_at DESC',
    );
    return result.map((json) => ChatModel.fromDatabase(json)).toList();
  }
}
