import 'package:flutter/material.dart';
import 'package:chatui/viewmodels/artifact_viewmodel.dart';
import 'package:chatui/models/document_model.dart';
import 'package:chatui/models/suggestion_model.dart';
import 'package:chatui/views/components/artifact_components/text_editor.dart';
import 'package:chatui/views/components/artifact_components/code_editor.dart';
import 'package:chatui/views/components/artifact_components/image_editor.dart';
import 'package:chatui/views/components/artifact_components/sheet_editor.dart';
import 'package:chatui/views/components/ui/loading_indicator.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:chatui/utils/error_handler.dart';
import 'package:chatui/repositories/document_repository.dart';
import 'package:chatui/repositories/suggestion_repository.dart';
import 'package:chatui/services/database_service.dart';
import 'package:provider/provider.dart';

class ArtifactPage extends StatefulWidget {
  final String documentId;

  const ArtifactPage({Key? key, required this.documentId}) : super(key: key);

  @override
  State<ArtifactPage> createState() => _ArtifactPageState();
}

class _ArtifactPageState extends State<ArtifactPage> {
  late ArtifactViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    final dbService = DatabaseService();
    final documentRepository = SqliteDocumentRepository(dbService);
    final suggestionRepository = SqliteSuggestionRepository(dbService);
    _viewModel = ArtifactViewModel(
      documentRepository: documentRepository,
      suggestionRepository: suggestionRepository,
    );
    _viewModel.loadDocument(widget.documentId);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<ArtifactViewModel>(
        builder: (context, vm, child) {
          return Scaffold(
            appBar: AppBar(
              title: Text(vm.currentDocument?.title ?? 'Artifact'),
              actions: [
                IconButton(
                  icon: Icon(Icons.save),
                  onPressed:
                      vm.currentDocument != null
                          ? () => vm.saveCurrentDocument()
                          : null,
                ),
              ],
            ),
            body: _buildBody(vm),
          );
        },
      ),
    );
  }

  Widget _buildBody(ArtifactViewModel vm) {
    if (vm.isLoading && vm.currentDocument == null) {
      return const Center(child: LoadingIndicator());
    }

    if (vm.error != null) {
      return Center(child: Text('Error: ${vm.error}'));
    }

    if (vm.currentDocument == null) {
      return const Center(child: Text('No document found.'));
    }

    switch (vm.currentDocument!.kind) {
      case DocumentKind.text:
        return TextEditor(key: ValueKey(vm.currentDocument!.id), viewModel: vm);
      case DocumentKind.code:
        return CodeEditor(key: ValueKey(vm.currentDocument!.id), viewModel: vm);
      case DocumentKind.image:
        return ImageEditor(
          key: ValueKey(vm.currentDocument!.id),
          viewModel: vm,
        );
      case DocumentKind.sheet:
        return SheetEditor(
          key: ValueKey(vm.currentDocument!.id),
          viewModel: vm,
        );
    }
  }
}
