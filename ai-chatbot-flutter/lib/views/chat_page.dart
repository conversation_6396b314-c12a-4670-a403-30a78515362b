import 'package:chatui/utils/error_handler.dart';
import 'package:chatui/viewmodels/model_selector_viewmodel.dart';
import 'package:chatui/viewmodels/visibility_viewmodel.dart';
import 'package:flutter/material.dart';
import '../viewmodels/chat_viewmodel.dart';
import 'components/chat_header.dart';
import 'components/message_list.dart';
import 'components/multimodal_input.dart';
import 'package:chatui/repositories/chat_repository.dart';
import 'package:chatui/repositories/message_repository.dart';
import 'package:chatui/repositories/vote_repository.dart';
import 'package:chatui/repositories/attachment_repository.dart';
import 'package:chatui/services/database_service.dart';
import 'package:provider/provider.dart';

class ChatPage extends StatefulWidget {
  final String? chatId;

  const ChatPage({super.key, this.chatId});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late ChatViewModel _chatViewModel;
  late ModelSelectorViewModel _modelSelectorViewModel;
  late VisibilityViewModel _visibilityViewModel;

  @override
  void initState() {
    super.initState();
    _modelSelectorViewModel = ModelSelectorViewModel();
    _visibilityViewModel = VisibilityViewModel();

    final dbService = DatabaseService();
    final chatRepository = SqliteChatRepository(dbService);
    final messageRepository = SqliteMessageRepository(dbService);
    final voteRepository = SqliteVoteRepository(dbService);
    final attachmentRepository = SqliteAttachmentRepository(dbService);

    _chatViewModel = ChatViewModel(
      chatRepository: chatRepository,
      messageRepository: messageRepository,
      voteRepository: voteRepository,
      attachmentRepository: attachmentRepository,
    );

    if (widget.chatId != null) {
      _chatViewModel.loadChat(widget.chatId!);
    }

    _chatViewModel.addListener(_onError);
  }

  @override
  void dispose() {
    _chatViewModel.removeListener(_onError);
    _chatViewModel.dispose();
    _modelSelectorViewModel.dispose();
    _visibilityViewModel.dispose();
    super.dispose();
  }

  void _onError() {
    if (_chatViewModel.error != null) {
      ErrorHandler.showErrorDialog(context, message: _chatViewModel.error!);
      _chatViewModel.clearError();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatHeader(
        chatViewModel: _chatViewModel,
        modelSelectorViewModel: _modelSelectorViewModel,
        visibilityViewModel: _visibilityViewModel,
      ),
      body: ChangeNotifierProvider.value(
        value: _chatViewModel,
        child: Column(
          children: [
            Expanded(child: MessageList()),
            MultimodalInput(viewModel: _chatViewModel),
          ],
        ),
      ),
    );
  }
}
