import 'package:flutter/material.dart';
import '../../viewmodels/chat_list_viewmodel.dart';
import 'chat_history_item.dart';

class ChatHistoryList extends StatefulWidget {
  final ChatListViewModel viewModel;
  final String? selectedChatId;
  final Function(String) onChatSelected;

  const ChatHistoryList({
    super.key,
    required this.viewModel,
    required this.selectedChatId,
    required this.onChatSelected,
  });

  @override
  State<ChatHistoryList> createState() => _ChatHistoryListState();
}

class _ChatHistoryListState extends State<ChatHistoryList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      widget.viewModel.loadMoreChats();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.viewModel,
      builder: (context, child) {
        if (widget.viewModel.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final groupedChats = widget.viewModel.groupedChats;

        if (groupedChats.isEmpty) {
          return const Center(
            child: Text('暂无聊天记录'),
          );
        }

        return ListView.builder(
          controller: _scrollController,
          itemCount:
              groupedChats.length + (widget.viewModel.isLoadingMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == groupedChats.length) {
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              );
            }

            final entry = groupedChats.entries.elementAt(index);
            final groupTitle = entry.key;
            final chats = entry.value;

            if (chats.isEmpty) {
              return const SizedBox.shrink();
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 分组标题
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Text(
                    groupTitle,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                        ),
                  ),
                ),
                // 聊天列表
                ...chats.map(
                  (chat) => ChatHistoryItem(
                    chat: chat,
                    isSelected: chat.id == widget.selectedChatId,
                    onTap: () => widget.onChatSelected(chat.id),
                    onDelete: (chatId) =>
                        widget.viewModel.deleteChat(context, chatId),
                    onRename: (chatId) =>
                        widget.viewModel.renameChat(context, chatId),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
