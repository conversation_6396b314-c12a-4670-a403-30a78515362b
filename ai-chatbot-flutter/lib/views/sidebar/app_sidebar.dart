import 'package:chatui/utils/error_handler.dart';
import 'package:flutter/material.dart';
import 'package:chatui/viewmodels/chat_list_viewmodel.dart';
import 'chat_history_list.dart';

class AppSidebar extends StatefulWidget {
  final ChatListViewModel viewModel;
  final String? selectedChatId;
  final Function(String) onChatSelected;
  final VoidCallback onNewChat;

  const AppSidebar({
    super.key,
    required this.viewModel,
    required this.selectedChatId,
    required this.onChatSelected,
    required this.onNewChat,
  });

  @override
  State<AppSidebar> createState() => _AppSidebarState();
}

class _AppSidebarState extends State<AppSidebar> {
  @override
  void initState() {
    super.initState();
    widget.viewModel.addListener(_onError);
  }

  @override
  void dispose() {
    widget.viewModel.removeListener(_onError);
    super.dispose();
  }

  void _onError() {
    if (widget.viewModel.error != null) {
      ErrorHandler.showErrorDialog(context, message: widget.viewModel.error!);
      widget.viewModel.clearError();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Column(
        children: [
          // 头部区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 新建聊天按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: widget.onNewChat,
                    icon: const Icon(Icons.add),
                    label: const Text('新建聊天'),
                  ),
                ),
                const SizedBox(height: 16),
                // 搜索框
                TextField(
                  decoration: const InputDecoration(
                    hintText: '搜索聊天记录...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  onChanged: widget.viewModel.setSearchQuery,
                ),
              ],
            ),
          ),
          // 聊天历史列表
          Expanded(
            child: ChatHistoryList(
              viewModel: widget.viewModel,
              selectedChatId: widget.selectedChatId,
              onChatSelected: widget.onChatSelected,
            ),
          ),
        ],
      ),
    );
  }
}
