import 'package:chatui/models/attachment_model.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class AttachmentPreview extends StatelessWidget {
  const AttachmentPreview({
    super.key,
    required this.attachments,
    required this.onRemoveAttachment,
  });

  final List<AttachmentModel> attachments;
  final ValueChanged<AttachmentModel> onRemoveAttachment;

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(8),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 8.0,
        children:
            attachments.map((attachment) {
              return Chip(
                label: Text(attachment.fileName),
                onDeleted: () => onRemoveAttachment(attachment),
                deleteIcon: const Icon(Icons.close, size: 18),
              );
            }).toList(),
      ),
    );
  }
}
