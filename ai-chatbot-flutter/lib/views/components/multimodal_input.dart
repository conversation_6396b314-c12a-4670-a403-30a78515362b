import 'dart:io';
import 'package:chatui/models/attachment_model.dart';
import 'package:chatui/viewmodels/chat_viewmodel.dart';
import 'package:chatui/views/components/attachment_preview.dart';
import 'package:chatui/views/components/suggested_actions.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class MultimodalInput extends StatefulWidget {
  final ChatViewModel viewModel;

  const MultimodalInput({super.key, required this.viewModel});

  @override
  State<MultimodalInput> createState() => _MultimodalInputState();
}

class _MultimodalInputState extends State<MultimodalInput> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final content = _controller.text.trim();
    if (content.isEmpty && widget.viewModel.pendingAttachments.isEmpty) return;

    widget.viewModel.sendMessage(content);
    _controller.clear();
    _focusNode.requestFocus();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      await widget.viewModel.addAttachment(image.path);
    }
  }

  Future<void> _pickFile() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      for (final path in result.paths) {
        if (path != null) {
          await widget.viewModel.addAttachment(path);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.viewModel,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).colorScheme.outline.withAlpha(51),
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 建议操作
              SuggestedActions(
                suggestions: widget.viewModel.suggestions,
                onSuggestionTapped: (suggestion) {
                  _controller.text = suggestion;
                  _sendMessage();
                },
              ),
              // 附件预览
              AttachmentPreview(
                attachments: widget.viewModel.pendingAttachments,
                onRemoveAttachment: widget.viewModel.removeAttachment,
              ),
              // 输入区域
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // 附件按钮
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.attach_file,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withAlpha(153),
                    ),
                    onSelected: (value) {
                      switch (value) {
                        case 'image':
                          _pickImage();
                          break;
                        case 'file':
                          _pickFile();
                          break;
                      }
                    },
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'image',
                            child: Row(
                              children: [
                                Icon(Icons.image, size: 16),
                                SizedBox(width: 8),
                                Text('选择图片'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'file',
                            child: Row(
                              children: [
                                Icon(Icons.insert_drive_file, size: 16),
                                SizedBox(width: 8),
                                Text('选择文件'),
                              ],
                            ),
                          ),
                        ],
                  ),
                  // 文本输入框
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      maxLines: null,
                      enabled: !widget.viewModel.isGenerating,
                      decoration: InputDecoration(
                        hintText:
                            widget.viewModel.isGenerating
                                ? '正在生成回复...'
                                : '输入消息...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      onSubmitted: (_) => _sendMessage(),
                      textInputAction: TextInputAction.send,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 发送按钮
                  FilledButton(
                    onPressed:
                        widget.viewModel.isGenerating ? null : _sendMessage,
                    style: FilledButton.styleFrom(
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(12),
                    ),
                    child:
                        widget.viewModel.isGenerating
                            ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            )
                            : const Icon(Icons.send),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return Icons.image;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      default:
        return Icons.insert_drive_file;
    }
  }
}
