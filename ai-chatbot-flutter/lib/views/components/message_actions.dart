import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/message_model.dart';
import '../../models/vote_model.dart';

class MessageActions extends StatelessWidget {
  final MessageModel message;
  final VoteModel? vote;
  final Function(String, bool) onVote;
  final Function(String, String) onEdit;
  final Function(String) onRegenerate;
  final Function(String) onDelete;

  const MessageActions({
    super.key,
    required this.message,
    this.vote,
    required this.onVote,
    required this.onEdit,
    required this.onRegenerate,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 点赞按钮
        IconButton(
          icon: Icon(
            vote?.isUpvoted == true ? Icons.thumb_up : Icons.thumb_up_outlined,
            size: 16,
          ),
          onPressed: () => onVote(message.id, true),
          tooltip: '点赞',
          style: IconButton.styleFrom(
            foregroundColor: vote?.isUpvoted == true
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
        ),
        // 点踩按钮
        IconButton(
          icon: Icon(
            vote?.isUpvoted == false
                ? Icons.thumb_down
                : Icons.thumb_down_outlined,
            size: 16,
          ),
          onPressed: () => onVote(message.id, false),
          tooltip: '点踩',
          style: IconButton.styleFrom(
            foregroundColor: vote?.isUpvoted == false
                ? Theme.of(context).colorScheme.error
                : Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
        ),
        // 复制按钮
        IconButton(
          icon: const Icon(Icons.copy, size: 16),
          onPressed: () {
            Clipboard.setData(ClipboardData(text: message.content));
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('已复制到剪贴板')),
            );
          },
          tooltip: '复制',
          style: IconButton.styleFrom(
            foregroundColor:
                Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
        ),
        // 重新生成按钮
        IconButton(
          icon: const Icon(Icons.refresh, size: 16),
          onPressed: () => onRegenerate(message.id),
          tooltip: '重新生成',
          style: IconButton.styleFrom(
            foregroundColor:
                Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
        ),
        // 更多操作菜单
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_horiz,
            size: 16,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
          ),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditDialog(context);
                break;
              case 'delete':
                _showDeleteDialog(context);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16),
                  SizedBox(width: 8),
                  Text('删除'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showEditDialog(BuildContext context) {
    final controller = TextEditingController(text: message.content);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑消息'),
        content: TextField(
          controller: controller,
          maxLines: null,
          decoration: const InputDecoration(
            hintText: '请输入消息内容...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final newContent = controller.text.trim();
              if (newContent.isNotEmpty && newContent != message.content) {
                onEdit(message.id, newContent);
              }
              Navigator.of(context).pop();
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除消息'),
        content: const Text('确定要删除这条消息吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              onDelete(message.id);
              Navigator.of(context).pop();
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
