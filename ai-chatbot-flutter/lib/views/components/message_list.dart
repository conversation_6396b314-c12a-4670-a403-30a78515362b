import 'package:flutter/material.dart';
import '../../viewmodels/chat_viewmodel.dart';
import 'message_item.dart';
import 'package:chatui/models/message_model.dart';
import 'package:chatui/views/components/ui/loading_indicator.dart';
import 'package:provider/provider.dart';

class MessageList extends StatefulWidget {
  const MessageList({super.key});

  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<ChatViewModel>();

    // 监听消息列表的变化，并滚动到底部
    // 使用 select 来确保只有在消息数量变化时才触发滚动
    context.select((ChatViewModel vm) => vm.messages.length);
    _scrollToBottom();

    if (vm.isLoading && vm.messages.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (vm.messages.isEmpty) {
      return const Center(child: Text('没有消息。开始对话吧！'));
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: vm.messages.length,
      itemBuilder: (context, index) {
        final message = vm.messages[index];
        final vote = vm.votes[message.id];
        final isLastMessage = index == vm.messages.length - 1;

        // 如果是最后一条消息并且正在生成，则使用ValueListenableBuilder
        if (isLastMessage && vm.isGenerating) {
          return ValueListenableBuilder<MessageModel?>(
            valueListenable: vm.streamingMessage,
            builder: (context, streamingMessage, child) {
              return MessageItem(
                key: ValueKey(message.id),
                message: streamingMessage ?? message,
                vote: vote,
                onVote: vm.voteMessage,
                onEdit: vm.editMessage,
                onRegenerate: vm.regenerateMessage,
                onDelete: vm.deleteMessage,
              );
            },
          );
        }

        return MessageItem(
          key: ValueKey(message.id),
          message: message,
          vote: vote,
          onVote: vm.voteMessage,
          onEdit: vm.editMessage,
          onRegenerate: vm.regenerateMessage,
          onDelete: vm.deleteMessage,
        );
      },
    );
  }
}
