import 'package:flutter/material.dart';
import '../../viewmodels/visibility_viewmodel.dart';

class VisibilitySelector extends StatelessWidget {
  final VisibilityViewModel viewModel;

  const VisibilitySelector({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: viewModel,
      builder: (context, child) {
        final isPrivate = viewModel.currentVisibility == ChatVisibility.private;
        final icon = isPrivate ? Icons.lock_outline : Icons.public;
        final name = isPrivate ? 'Private' : 'Public';

        return PopupMenuButton<ChatVisibility>(
          tooltip: "Select Visibility",
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
              ),
              const SizedBox(width: 4),
              Text(
                name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha(178),
                    ),
              ),
              const SizedBox(width: 2),
              Icon(
                Icons.arrow_drop_down,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
              ),
            ],
          ),
          onSelected: (value) {
            viewModel.setVisibility(value);
          },
          itemBuilder: (context) => ChatVisibility.values.map((visibility) {
            final isSelected = visibility == viewModel.currentVisibility;
            final itemIcon = visibility == ChatVisibility.private
                ? Icons.lock_outline
                : Icons.public;
            final itemName =
                visibility == ChatVisibility.private ? 'Private' : 'Public';
            final itemDescription = visibility == ChatVisibility.private
                ? 'Only you can see this chat.'
                : 'This chat is public and shareable.';

            return PopupMenuItem<ChatVisibility>(
              value: visibility,
              child: Row(
                children: [
                  Icon(
                    isSelected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    size: 16,
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    itemIcon,
                    size: 16,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(178),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          itemName,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: isSelected
                                        ? FontWeight.w500
                                        : FontWeight.normal,
                                  ),
                        ),
                        Text(
                          itemDescription,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withAlpha(153),
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
