import 'package:chatui/viewmodels/model_selector_viewmodel.dart';
import 'package:chatui/viewmodels/visibility_viewmodel.dart';
import 'package:flutter/material.dart';
import 'package:chatui/viewmodels/chat_viewmodel.dart';
import 'package:chatui/views/components/model_selector.dart';
import 'package:chatui/views/components/visibility_selector.dart';

class ChatHeader extends StatelessWidget implements PreferredSizeWidget {
  final ChatViewModel chatViewModel;
  final ModelSelectorViewModel modelSelectorViewModel;
  final VisibilityViewModel visibilityViewModel;

  const ChatHeader({
    super.key,
    required this.chatViewModel,
    required this.modelSelectorViewModel,
    required this.visibilityViewModel,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: AnimatedBuilder(
        animation: chatViewModel,
        builder: (context, child) {
          final chat = chatViewModel.currentChat;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                chat?.title ?? '新建聊天',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              if (chatViewModel.isGenerating)
                Text(
                  '正在生成回复...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha((255 * 0.6).round()),
                  ),
                ),
            ],
          );
        },
      ),
      actions: [
        // 模型选择器
        ModelSelector(viewModel: modelSelectorViewModel),
        const SizedBox(width: 8),
        // 可见性选择器
        VisibilitySelector(viewModel: visibilityViewModel),
        const SizedBox(width: 8),
        // 更多操作菜单
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'clear':
                _showClearDialog(context);
                break;
              case 'export':
                // TODO: 实现导出功能
                break;
              case 'settings':
                // TODO: 实现设置功能
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'clear',
                  child: Row(
                    children: [
                      Icon(Icons.clear_all, size: 16),
                      SizedBox(width: 8),
                      Text('清空对话'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'export',
                  child: Row(
                    children: [
                      Icon(Icons.download, size: 16),
                      SizedBox(width: 8),
                      Text('导出对话'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings, size: 16),
                      SizedBox(width: 8),
                      Text('设置'),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  void _showClearDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('清空对话'),
            content: const Text('确定要清空当前对话吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  chatViewModel.clearChat();
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
