import 'package:chatui/viewmodels/artifact_viewmodel.dart';
import 'package:flutter/material.dart';

class TextEditor extends StatefulWidget {
  final ArtifactViewModel viewModel;

  const TextEditor({Key? key, required this.viewModel}) : super(key: key);

  @override
  State<TextEditor> createState() => _TextEditorState();
}

class _TextEditorState extends State<TextEditor> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.viewModel.currentDocument?.content ?? '',
    );
    _controller.addListener(_onTextChanged);
  }

  @override
  void didUpdateWidget(covariant TextEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.viewModel.currentDocument?.content != _controller.text) {
      _controller.text = widget.viewModel.currentDocument?.content ?? '';
    }
  }

  void _onTextChanged() {
    widget.viewModel.updateContent(_controller.text);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _controller,
        maxLines: null,
        expands: true,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: 'Enter your text here...',
        ),
      ),
    );
  }
}
