import 'package:chatui/viewmodels/artifact_viewmodel.dart';
import 'package:flutter/material.dart';

class CodeEditor extends StatefulWidget {
  final ArtifactViewModel viewModel;

  const CodeEditor({Key? key, required this.viewModel}) : super(key: key);

  @override
  State<CodeEditor> createState() => _CodeEditorState();
}

class _CodeEditorState extends State<CodeEditor> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.viewModel.currentDocument?.content ?? '',
    );
    _controller.addListener(_onTextChanged);
  }

  @override
  void didUpdateWidget(covariant CodeEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.viewModel.currentDocument?.content != _controller.text) {
      _controller.text = widget.viewModel.currentDocument?.content ?? '';
    }
  }

  void _onTextChanged() {
    widget.viewModel.updateContent(_controller.text);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // A simplified code editor. For real syntax highlighting,
    // a more advanced package like 'code_text_field' would be needed.
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _controller,
        maxLines: null,
        expands: true,
        style: TextStyle(fontFamily: 'monospace'),
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: 'Enter your code here...',
        ),
      ),
    );
  }
}
