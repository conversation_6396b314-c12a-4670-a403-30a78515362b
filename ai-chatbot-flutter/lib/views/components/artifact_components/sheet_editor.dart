import 'package:chatui/viewmodels/artifact_viewmodel.dart';
import 'package:flutter/material.dart';

class SheetEditor extends StatefulWidget {
  final ArtifactViewModel viewModel;

  const SheetEditor({Key? key, required this.viewModel}) : super(key: key);

  @override
  _SheetEditorState createState() => _SheetEditorState();
}

class _SheetEditorState extends State<SheetEditor> {
  late List<List<TextEditingController>> _controllers;
  List<List<String>> _data = [];

  @override
  void initState() {
    super.initState();
    _parseData();
  }

  void _parseData() {
    final content = widget.viewModel.currentDocument?.content ?? '';
    _data = content.split('\n').map((line) => line.split(',')).toList();
    _controllers =
        _data.map((row) {
          return row.map((cell) => TextEditingController(text: cell)).toList();
        }).toList();

    for (var i = 0; i < _controllers.length; i++) {
      for (var j = 0; j < _controllers[i].length; j++) {
        _controllers[i][j].addListener(() => _updateViewModel(i, j));
      }
    }
  }

  void _updateViewModel(int row, int col) {
    _data[row][col] = _controllers[row][col].text;
    final newContent = _data.map((row) => row.join(',')).join('\n');
    widget.viewModel.updateContent(newContent);
  }

  @override
  void didUpdateWidget(covariant SheetEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.viewModel.currentDocument?.content !=
        oldWidget.viewModel.currentDocument?.content) {
      _parseData();
    }
  }

  @override
  void dispose() {
    for (var row in _controllers) {
      for (var controller in row) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns:
              _data.isNotEmpty
                  ? List.generate(
                    _data.first.length,
                    (i) => DataColumn(label: Text('Col ${i + 1}')),
                  )
                  : [],
          rows: List.generate(_controllers.length, (i) {
            return DataRow(
              cells: List.generate(_controllers[i].length, (j) {
                return DataCell(
                  TextField(
                    controller: _controllers[i][j],
                    decoration: InputDecoration(border: InputBorder.none),
                  ),
                );
              }),
            );
          }),
        ),
      ),
    );
  }
}
