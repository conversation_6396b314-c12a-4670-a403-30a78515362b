import 'dart:io';
import 'package:chatui/viewmodels/artifact_viewmodel.dart';
import 'package:flutter/material.dart';

class ImageEditor extends StatelessWidget {
  final ArtifactViewModel viewModel;

  const ImageEditor({Key? key, required this.viewModel}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final imagePath = viewModel.currentDocument?.content;
    if (imagePath == null || imagePath.isEmpty) {
      return const Center(child: Text('No image path provided.'));
    }

    final imageFile = File(imagePath);
    if (!imageFile.existsSync()) {
      return Center(child: Text('Image not found at path: $imagePath'));
    }

    return InteractiveViewer(child: Image.file(imageFile));
  }
}
