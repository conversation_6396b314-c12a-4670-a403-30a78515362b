import 'package:flutter/material.dart';
import '../viewmodels/chat_list_viewmodel.dart';
import 'sidebar/app_sidebar.dart';
import 'chat_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late ChatListViewModel _chatListViewModel;
  String? _selectedChatId;

  @override
  void initState() {
    super.initState();
    _chatListViewModel = ChatListViewModel();
    _chatListViewModel.loadChats();
  }

  @override
  void dispose() {
    _chatListViewModel.dispose();
    super.dispose();
  }

  void _onChatSelected(String chatId) {
    setState(() {
      _selectedChatId = chatId;
    });
  }

  Future<void> _onNewChat() async {
    try {
      final newChat = await _chatListViewModel.createNewChat();
      if (mounted) {
        setState(() {
          _selectedChatId = newChat.id;
        });
      }
    } catch (e) {
      // 在这里处理错误，例如显示一个SnackBar
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('创建新聊天失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isWide = constraints.maxWidth > 768;

          if (isWide) {
            // 宽屏布局：侧边栏 + 聊天区域
            return Row(
              children: [
                SizedBox(
                  width: 300,
                  child: AppSidebar(
                    viewModel: _chatListViewModel,
                    selectedChatId: _selectedChatId,
                    onChatSelected: _onChatSelected,
                    onNewChat: _onNewChat,
                  ),
                ),
                Expanded(
                  child:
                      _selectedChatId != null
                          ? ChatPage(chatId: _selectedChatId!)
                          : const _WelcomeScreen(),
                ),
              ],
            );
          } else {
            // 窄屏布局：使用Drawer
            return Scaffold(
              drawer: SizedBox(
                width: 280,
                child: Drawer(
                  child: AppSidebar(
                    viewModel: _chatListViewModel,
                    selectedChatId: _selectedChatId,
                    onChatSelected: (chatId) {
                      _onChatSelected(chatId);
                      Navigator.pop(context); // 关闭drawer
                    },
                    onNewChat: () {
                      _onNewChat().then((_) {
                        if (context.mounted) {
                          Navigator.pop(context); // 关闭drawer
                        }
                      });
                    },
                  ),
                ),
              ),
              appBar: AppBar(
                title: const Text('AI Chatbot'),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: _onNewChat,
                  ),
                ],
              ),
              body:
                  _selectedChatId != null
                      ? ChatPage(chatId: _selectedChatId!)
                      : const _WelcomeScreen(),
            );
          }
        },
      ),
    );
  }
}

class _WelcomeScreen extends StatelessWidget {
  const _WelcomeScreen();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            '欢迎使用 AI Chatbot',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text('选择一个聊天记录或开始新对话', style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }
}
