/// 天气数据模型
class WeatherData {
  final String location;
  final double temperature;
  final double feelsLike;
  final int humidity;
  final double pressure;
  final double windSpeed;
  final int windDirection;
  final double visibility;
  final int uvIndex;
  final String condition;
  final String description;
  final String icon;
  final DateTime timestamp;

  WeatherData({
    required this.location,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.pressure,
    required this.windSpeed,
    required this.windDirection,
    required this.visibility,
    required this.uvIndex,
    required this.condition,
    required this.description,
    required this.icon,
    required this.timestamp,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      location: json['name'] ?? 'Unknown',
      temperature: (json['main']['temp'] ?? 0.0).toDouble(),
      feelsLike: (json['main']['feels_like'] ?? 0.0).toDouble(),
      humidity: json['main']['humidity'] ?? 0,
      pressure: (json['main']['pressure'] ?? 0.0).toDouble(),
      windSpeed: (json['wind']['speed'] ?? 0.0).toDouble(),
      windDirection: json['wind']['deg'] ?? 0,
      visibility: (json['visibility'] ?? 0.0) / 1000, // m to km
      uvIndex: 0, // UV Index is not in current weather data from OpenWeatherMap
      condition: json['weather'][0]['main'] ?? 'Unknown',
      description: json['weather'][0]['description'] ?? 'No description',
      icon: _mapWeatherIcon(json['weather'][0]['icon'] ?? ''),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['dt'] * 1000),
    );
  }

  WeatherData copyWith({
    String? location,
    double? temperature,
    double? feelsLike,
    int? humidity,
    double? pressure,
    double? windSpeed,
    int? windDirection,
    double? visibility,
    int? uvIndex,
    String? condition,
    String? description,
    String? icon,
    DateTime? timestamp,
  }) {
    return WeatherData(
      location: location ?? this.location,
      temperature: temperature ?? this.temperature,
      feelsLike: feelsLike ?? this.feelsLike,
      humidity: humidity ?? this.humidity,
      pressure: pressure ?? this.pressure,
      windSpeed: windSpeed ?? this.windSpeed,
      windDirection: windDirection ?? this.windDirection,
      visibility: visibility ?? this.visibility,
      uvIndex: uvIndex ?? this.uvIndex,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  static String _mapWeatherIcon(String iconCode) {
    switch (iconCode) {
      case '01d':
        return '☀️';
      case '01n':
        return '🌙';
      case '02d':
        return '⛅️';
      case '02n':
        return '☁️';
      case '03d':
      case '03n':
        return '☁️';
      case '04d':
      case '04n':
        return '☁️';
      case '09d':
      case '09n':
        return '🌧️';
      case '10d':
        return '🌦️';
      case '10n':
        return '🌧️';
      case '11d':
      case '11n':
        return '⛈️';
      case '13d':
      case '13n':
        return '❄️';
      case '50d':
      case '50n':
        return '🌫️';
      default:
        return '❓';
    }
  }
}

/// 城市数据模型
class CityData {
  final String name;
  final String country;
  final double latitude;
  final double longitude;

  CityData({
    required this.name,
    required this.country,
    required this.latitude,
    required this.longitude,
  });

  factory CityData.fromJson(Map<String, dynamic> json) {
    return CityData(
      name: json['name'] ?? 'Unknown',
      country: json['country'] ?? 'Unknown',
      latitude: (json['lat'] ?? 0.0).toDouble(),
      longitude: (json['lon'] ?? 0.0).toDouble(),
    );
  }
}

/// 空气质量数据模型
class AirQualityData {
  final int aqi;
  final double pm25;
  final double pm10;
  final double o3;
  final double no2;
  final double so2;
  final double co;
  final DateTime timestamp;

  AirQualityData({
    required this.aqi,
    required this.pm25,
    required this.pm10,
    required this.o3,
    required this.no2,
    required this.so2,
    required this.co,
    required this.timestamp,
  });

  factory AirQualityData.fromJson(Map<String, dynamic> json) {
    final aqiData = json['list'][0];
    return AirQualityData(
      aqi: aqiData['main']['aqi'] ?? 0,
      pm25: (aqiData['components']['pm2_5'] ?? 0.0).toDouble(),
      pm10: (aqiData['components']['pm10'] ?? 0.0).toDouble(),
      o3: (aqiData['components']['o3'] ?? 0.0).toDouble(),
      no2: (aqiData['components']['no2'] ?? 0.0).toDouble(),
      so2: (aqiData['components']['so2'] ?? 0.0).toDouble(),
      co: (aqiData['components']['co'] ?? 0.0).toDouble(),
      timestamp: DateTime.fromMillisecondsSinceEpoch(aqiData['dt'] * 1000),
    );
  }
}
