import 'dart:convert';

enum DocumentKind {
  text,
  code,
  image,
  sheet;

  String get value => name;

  static DocumentKind fromString(String value) {
    switch (value.toLowerCase()) {
      case 'text':
        return DocumentKind.text;
      case 'code':
        return DocumentKind.code;
      case 'image':
        return DocumentKind.image;
      case 'sheet':
        return DocumentKind.sheet;
      default:
        return DocumentKind.text;
    }
  }
}

class DocumentModel {
  final String id;
  final String title;
  final String content;
  final DocumentKind kind;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;
  final String? parentId;
  final Map<String, dynamic>? metadata;

  const DocumentModel({
    required this.id,
    required this.title,
    required this.content,
    required this.kind,
    required this.createdAt,
    required this.updatedAt,
    this.version = 1,
    this.parentId,
    this.metadata,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      kind: DocumentKind.fromString(json['kind'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      version: json['version'] as int? ?? 1,
      parentId: json['parent_id'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'kind': kind.value,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
      'parent_id': parentId,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'kind': kind.value,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
      'parent_id': parentId,
      'metadata': metadata != null ? jsonEncode(metadata!) : null,
    };
  }

  factory DocumentModel.fromDatabase(Map<String, dynamic> map) {
    return DocumentModel(
      id: map['id'] as String,
      title: map['title'] as String,
      content: map['content'] as String,
      kind: DocumentKind.fromString(map['kind'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      version: map['version'] as int? ?? 1,
      parentId: map['parent_id'] as String?,
      metadata:
          map['metadata'] != null
              ? jsonDecode(map['metadata'] as String)
              : null,
    );
  }

  DocumentModel copyWith({
    String? id,
    String? title,
    String? content,
    DocumentKind? kind,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? version,
    String? parentId,
    Map<String, dynamic>? metadata,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      kind: kind ?? this.kind,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      version: version ?? this.version,
      parentId: parentId ?? this.parentId,
      metadata: metadata ?? this.metadata,
    );
  }

  // 创建新版本
  DocumentModel createNewVersion({
    String? newId,
    String? newTitle,
    String? newContent,
    Map<String, dynamic>? newMetadata,
  }) {
    return DocumentModel(
      id: newId ?? id,
      title: newTitle ?? title,
      content: newContent ?? content,
      kind: kind,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      version: version + 1,
      parentId: parentId ?? id, // 设置当前文档为父文档
      metadata: newMetadata ?? metadata,
    );
  }

  // 文档类型相关的getter
  bool get isTextDocument => kind == DocumentKind.text;
  bool get isCodeDocument => kind == DocumentKind.code;
  bool get isImageDocument => kind == DocumentKind.image;
  bool get isSheetDocument => kind == DocumentKind.sheet;

  // 编程语言检测（针对代码文档）
  String? get programmingLanguage {
    if (!isCodeDocument) return null;

    // 从metadata中获取或者通过内容推断
    if (metadata?['language'] != null) {
      return metadata!['language'] as String;
    }

    // 简单的语言推断逻辑
    if (content.contains('function ') ||
        content.contains('const ') ||
        content.contains('let ')) {
      return 'javascript';
    } else if (content.contains('def ') ||
        content.contains('import ') && content.contains('from ')) {
      return 'python';
    } else if (content.contains('class ') && content.contains('public ')) {
      return 'java';
    } else if (content.contains('#include') || content.contains('int main')) {
      return 'cpp';
    }

    return 'text';
  }

  // 文档图标
  String get iconName {
    switch (kind) {
      case DocumentKind.text:
        return 'description';
      case DocumentKind.code:
        return 'code';
      case DocumentKind.image:
        return 'image';
      case DocumentKind.sheet:
        return 'table_chart';
    }
  }

  // 内容预览（截取前100个字符）
  String get contentPreview {
    if (content.isEmpty) return '空文档';
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DocumentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DocumentModel(id: $id, title: $title, kind: ${kind.value}, version: $version)';
  }
}
