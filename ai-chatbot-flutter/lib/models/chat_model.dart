import 'dart:convert';

class ChatModel {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String visibility;
  final String modelId;
  final Map<String, dynamic>? metadata;

  const ChatModel({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.visibility = 'private',
    this.modelId = 'gpt-4',
    this.metadata,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      visibility: json['visibility'] as String? ?? 'private',
      modelId: json['model_id'] as String? ?? 'gpt-4',
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'visibility': visibility,
      'model_id': modelId,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'visibility': visibility,
      'model_id': modelId,
      'metadata': metadata != null ? jsonEncode(metadata!) : null,
    };
  }

  factory ChatModel.fromDatabase(Map<String, dynamic> map) {
    return ChatModel(
      id: map['id'] as String,
      title: map['title'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      visibility: map['visibility'] as String? ?? 'private',
      modelId: map['model_id'] as String? ?? 'gpt-4',
      metadata:
          map['metadata'] != null
              ? jsonDecode(map['metadata'] as String)
              : null,
    );
  }

  ChatModel copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? visibility,
    String? modelId,
    Map<String, dynamic>? metadata,
  }) {
    return ChatModel(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      visibility: visibility ?? this.visibility,
      modelId: modelId ?? this.modelId,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatModel(id: $id, title: $title, visibility: $visibility, modelId: $modelId)';
  }
}
