class VoteModel {
  final String id;
  final String messageId;
  final bool isUpvoted;
  final DateTime createdAt;

  const VoteModel({
    required this.id,
    required this.messageId,
    required this.isUpvoted,
    required this.createdAt,
  });

  factory VoteModel.fromJson(Map<String, dynamic> json) {
    return VoteModel(
      id: json['id'] as String,
      messageId: json['message_id'] as String,
      isUpvoted: json['is_upvoted'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message_id': messageId,
      'is_upvoted': isUpvoted,
      'created_at': createdAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'message_id': messageId,
      'is_upvoted': isUpvoted ? 1 : 0, // SQLite boolean as integer
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory VoteModel.fromDatabase(Map<String, dynamic> map) {
    return VoteModel(
      id: map['id'] as String,
      messageId: map['message_id'] as String,
      isUpvoted: (map['is_upvoted'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  VoteModel copyWith({
    String? id,
    String? messageId,
    bool? isUpvoted,
    DateTime? createdAt,
  }) {
    return VoteModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      isUpvoted: isUpvoted ?? this.isUpvoted,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // 获取投票类型
  String get voteType => isUpvoted ? 'upvote' : 'downvote';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoteModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VoteModel(id: $id, messageId: $messageId, type: $voteType)';
  }
}
