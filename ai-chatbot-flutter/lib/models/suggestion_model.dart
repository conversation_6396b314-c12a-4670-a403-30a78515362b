class SuggestionModel {
  final String id;
  final String documentId;
  final String description;
  final int? selectionStart;
  final int? selectionEnd;
  final DateTime createdAt;
  final bool isApplied;
  final String? appliedContent;

  const SuggestionModel({
    required this.id,
    required this.documentId,
    required this.description,
    this.selectionStart,
    this.selectionEnd,
    required this.createdAt,
    this.isApplied = false,
    this.appliedContent,
  });

  factory SuggestionModel.fromJson(Map<String, dynamic> json) {
    return SuggestionModel(
      id: json['id'] as String,
      documentId: json['document_id'] as String,
      description: json['description'] as String,
      selectionStart: json['selection_start'] as int?,
      selectionEnd: json['selection_end'] as int?,
      createdAt: DateTime.parse(json['created_at'] as String),
      isApplied: json['is_applied'] as bool? ?? false,
      appliedContent: json['applied_content'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'document_id': documentId,
      'description': description,
      'selection_start': selectionStart,
      'selection_end': selectionEnd,
      'created_at': createdAt.toIso8601String(),
      'is_applied': isApplied,
      'applied_content': appliedContent,
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'document_id': documentId,
      'description': description,
      'selection_start': selectionStart,
      'selection_end': selectionEnd,
      'created_at': createdAt.toIso8601String(),
      'is_applied': isApplied ? 1 : 0, // SQLite boolean as integer
      'applied_content': appliedContent,
    };
  }

  factory SuggestionModel.fromDatabase(Map<String, dynamic> map) {
    return SuggestionModel(
      id: map['id'] as String,
      documentId: map['document_id'] as String,
      description: map['description'] as String,
      selectionStart: map['selection_start'] as int?,
      selectionEnd: map['selection_end'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      isApplied: (map['is_applied'] as int) == 1,
      appliedContent: map['applied_content'] as String?,
    );
  }

  SuggestionModel copyWith({
    String? id,
    String? documentId,
    String? description,
    int? selectionStart,
    int? selectionEnd,
    DateTime? createdAt,
    bool? isApplied,
    String? appliedContent,
  }) {
    return SuggestionModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      description: description ?? this.description,
      selectionStart: selectionStart ?? this.selectionStart,
      selectionEnd: selectionEnd ?? this.selectionEnd,
      createdAt: createdAt ?? this.createdAt,
      isApplied: isApplied ?? this.isApplied,
      appliedContent: appliedContent ?? this.appliedContent,
    );
  }

  // 应用建议
  SuggestionModel apply(String content) {
    return copyWith(isApplied: true, appliedContent: content);
  }

  // 撤销建议
  SuggestionModel unapply() {
    return copyWith(isApplied: false, appliedContent: null);
  }

  // 是否有选择范围
  bool get hasSelection => selectionStart != null && selectionEnd != null;

  // 选择长度
  int get selectionLength {
    if (!hasSelection) return 0;
    return selectionEnd! - selectionStart!;
  }

  // 建议类型（根据描述推断）
  SuggestionType get suggestionType {
    final desc = description.toLowerCase();

    if (desc.contains('fix') ||
        desc.contains('correct') ||
        desc.contains('error')) {
      return SuggestionType.fix;
    } else if (desc.contains('improve') ||
        desc.contains('optimize') ||
        desc.contains('enhance')) {
      return SuggestionType.improvement;
    } else if (desc.contains('add') ||
        desc.contains('insert') ||
        desc.contains('include')) {
      return SuggestionType.addition;
    } else if (desc.contains('remove') ||
        desc.contains('delete') ||
        desc.contains('clean')) {
      return SuggestionType.removal;
    } else if (desc.contains('refactor') ||
        desc.contains('restructure') ||
        desc.contains('reorganize')) {
      return SuggestionType.refactor;
    }

    return SuggestionType.general;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SuggestionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SuggestionModel(id: $id, type: ${suggestionType.name}, applied: $isApplied)';
  }
}

enum SuggestionType {
  fix,
  improvement,
  addition,
  removal,
  refactor,
  general;

  String get displayName {
    switch (this) {
      case SuggestionType.fix:
        return '修复';
      case SuggestionType.improvement:
        return '改进';
      case SuggestionType.addition:
        return '添加';
      case SuggestionType.removal:
        return '删除';
      case SuggestionType.refactor:
        return '重构';
      case SuggestionType.general:
        return '一般';
    }
  }

  String get iconName {
    switch (this) {
      case SuggestionType.fix:
        return 'build';
      case SuggestionType.improvement:
        return 'trending_up';
      case SuggestionType.addition:
        return 'add';
      case SuggestionType.removal:
        return 'remove';
      case SuggestionType.refactor:
        return 'transform';
      case SuggestionType.general:
        return 'lightbulb';
    }
  }
}
