import 'dart:convert';

class AttachmentModel {
  final String id;
  final String messageId;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String contentType;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const AttachmentModel({
    required this.id,
    required this.messageId,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.contentType,
    required this.createdAt,
    this.metadata,
  });

  factory AttachmentModel.fromJson(Map<String, dynamic> json) {
    return AttachmentModel(
      id: json['id'] as String,
      messageId: json['message_id'] as String,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String,
      fileSize: json['file_size'] as int,
      contentType: json['content_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message_id': messageId,
      'file_name': fileName,
      'file_path': filePath,
      'file_size': fileSize,
      'content_type': contentType,
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'message_id': messageId,
      'file_name': fileName,
      'file_path': filePath,
      'file_size': fileSize,
      'content_type': contentType,
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata != null ? jsonEncode(metadata!) : null,
    };
  }

  factory AttachmentModel.fromDatabase(Map<String, dynamic> map) {
    return AttachmentModel(
      id: map['id'] as String,
      messageId: map['message_id'] as String,
      fileName: map['file_name'] as String,
      filePath: map['file_path'] as String,
      fileSize: map['file_size'] as int,
      contentType: map['content_type'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      metadata:
          map['metadata'] != null
              ? jsonDecode(map['metadata'] as String)
              : null,
    );
  }

  AttachmentModel copyWith({
    String? id,
    String? messageId,
    String? fileName,
    String? filePath,
    int? fileSize,
    String? contentType,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return AttachmentModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      contentType: contentType ?? this.contentType,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  // 文件类型判断
  bool get isImage => contentType.startsWith('image/');
  bool get isVideo => contentType.startsWith('video/');
  bool get isAudio => contentType.startsWith('audio/');
  bool get isDocument =>
      contentType.startsWith('application/') || contentType.startsWith('text/');
  bool get isPdf => contentType == 'application/pdf';
  bool get isText => contentType.startsWith('text/');

  // 文件大小格式化
  String get formattedSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  // 文件扩展名
  String get fileExtension {
    final lastDot = fileName.lastIndexOf('.');
    if (lastDot == -1) return '';
    return fileName.substring(lastDot + 1).toLowerCase();
  }

  // 文件图标类型
  String get iconType {
    if (isImage) return 'image';
    if (isVideo) return 'video';
    if (isAudio) return 'audio';
    if (isPdf) return 'pdf';
    if (isText) return 'text';
    return 'file';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttachmentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AttachmentModel(id: $id, fileName: $fileName, type: $contentType, size: $formattedSize)';
  }
}
