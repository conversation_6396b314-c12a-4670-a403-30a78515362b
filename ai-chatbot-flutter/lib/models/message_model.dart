import 'dart:convert';

class MessageModel {
  final String id;
  final String chatId;
  final String role; // 'user', 'assistant', 'system'
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String
  messageType; // 'text', 'image', 'file', 'tool_call', 'tool_result'
  final Map<String, dynamic>? metadata;
  final List<AttachmentReference>? attachments;

  const MessageModel({
    required this.id,
    required this.chatId,
    required this.role,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    this.messageType = 'text',
    this.metadata,
    this.attachments,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as String,
      chatId: json['chat_id'] as String,
      role: json['role'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messageType: json['message_type'] as String? ?? 'text',
      metadata: json['metadata'] as Map<String, dynamic>?,
      attachments:
          json['attachments'] != null
              ? (json['attachments'] as List)
                  .map((e) => AttachmentReference.fromJson(e))
                  .toList()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_id': chatId,
      'role': role,
      'content': content,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'message_type': messageType,
      'metadata': metadata,
      'attachments': attachments?.map((a) => a.toJson()).toList(),
    };
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'chat_id': chatId,
      'role': role,
      'content': content,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'message_type': messageType,
      'metadata': metadata != null ? jsonEncode(metadata!) : null,
    };
  }

  factory MessageModel.fromDatabase(Map<String, dynamic> map) {
    return MessageModel(
      id: map['id'] as String,
      chatId: map['chat_id'] as String,
      role: map['role'] as String,
      content: map['content'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      messageType: map['message_type'] as String? ?? 'text',
      metadata:
          map['metadata'] != null
              ? jsonDecode(map['metadata'] as String)
              : null,
    );
  }

  MessageModel copyWith({
    String? id,
    String? chatId,
    String? role,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? messageType,
    Map<String, dynamic>? metadata,
    List<AttachmentReference>? attachments,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      role: role ?? this.role,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messageType: messageType ?? this.messageType,
      metadata: metadata ?? this.metadata,
      attachments: attachments ?? this.attachments,
    );
  }

  bool get isUser => role == 'user';
  bool get isAssistant => role == 'assistant';
  bool get isSystem => role == 'system';
  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MessageModel(id: $id, role: $role, type: $messageType, hasAttachments: $hasAttachments)';
  }
}

// 附件引用类
class AttachmentReference {
  final String id;
  final String fileName;
  final String contentType;
  final int fileSize;
  final String? url;

  const AttachmentReference({
    required this.id,
    required this.fileName,
    required this.contentType,
    required this.fileSize,
    this.url,
  });

  factory AttachmentReference.fromJson(Map<String, dynamic> json) {
    return AttachmentReference(
      id: json['id'] as String,
      fileName: json['file_name'] as String,
      contentType: json['content_type'] as String,
      fileSize: json['file_size'] as int,
      url: json['url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_name': fileName,
      'content_type': contentType,
      'file_size': fileSize,
      'url': url,
    };
  }

  bool get isImage => contentType.startsWith('image/');
  bool get isDocument => contentType.startsWith('application/');
  bool get isVideo => contentType.startsWith('video/');
  bool get isAudio => contentType.startsWith('audio/');

  @override
  String toString() {
    return 'AttachmentReference(id: $id, fileName: $fileName, type: $contentType)';
  }
}
