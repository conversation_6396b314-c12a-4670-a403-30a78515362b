import 'dart:async';

import 'package:chatui/models/attachment_model.dart';
import 'package:chatui/repositories/attachment_repository.dart';
import 'package:chatui/repositories/chat_repository.dart';
import 'package:chatui/repositories/message_repository.dart';
import 'package:chatui/repositories/vote_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../models/vote_model.dart';
import '../services/file_service.dart';
import '../services/llm_service.dart';

class ChatViewModel extends ChangeNotifier {
  final IChatRepository _chatRepository;
  final IMessageRepository _messageRepository;
  final IVoteRepository _voteRepository;
  final IAttachmentRepository _attachmentRepository;

  final LLMService _llmService = LLMService();
  final FileService _fileService = FileService();

  ChatModel? _currentChat;
  List<MessageModel> _messages = [];
  Map<String, VoteModel> _votes = {};
  List<AttachmentModel> _attachments = [];
  final List<AttachmentModel> _pendingAttachments = [];
  List<String> _suggestions = [];
  bool _isLoading = false;
  bool _isGenerating = false;
  String? _error;
  StreamSubscription<String>? _streamSubscription;
  final ValueNotifier<MessageModel?> streamingMessage = ValueNotifier(null);

  // 设置
  String _selectedModel = 'gpt-4';
  String _visibility = 'private';

  // Getters
  ChatModel? get currentChat => _currentChat;
  List<MessageModel> get messages => List.unmodifiable(_messages);
  Map<String, VoteModel> get votes => Map.unmodifiable(_votes);
  List<AttachmentModel> get attachments => List.unmodifiable(_attachments);
  List<AttachmentModel> get pendingAttachments =>
      List.unmodifiable(_pendingAttachments);
  List<String> get suggestions => List.unmodifiable(_suggestions);
  bool get isLoading => _isLoading;
  bool get isGenerating => _isGenerating;
  String? get error => _error;
  String get selectedModel => _selectedModel;
  String get visibility => _visibility;

  ChatViewModel({
    required IChatRepository chatRepository,
    required IMessageRepository messageRepository,
    required IVoteRepository voteRepository,
    required IAttachmentRepository attachmentRepository,
  }) : _chatRepository = chatRepository,
       _messageRepository = messageRepository,
       _voteRepository = voteRepository,
       _attachmentRepository = attachmentRepository;

  // 公共方法，用于UI清除错误
  void clearError() {
    _error = null;
  }

  // 内部状态更新方法
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
    }
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setGenerating(bool value) {
    _isGenerating = value;
    notifyListeners();
  }

  // 切换模型
  Future<void> selectModel(String modelId) async {
    if (_selectedModel == modelId) return;

    _selectedModel = modelId;
    notifyListeners();

    if (_currentChat != null) {
      _currentChat = _currentChat!.copyWith(modelId: modelId);
      await _chatRepository.updateChat(_currentChat!);
    }
  }

  // 加载聊天和消息
  Future<void> loadChat(String chatId) async {
    _isLoading = true;
    notifyListeners();
    try {
      _currentChat = await _chatRepository.findChatById(chatId);
      _messages = await _messageRepository.getMessagesByChatId(chatId);
      _votes = await _voteRepository.getVotesByChatId(chatId);
      _attachments = await _attachmentRepository.getAttachmentsByChatId(chatId);

      if (_currentChat != null) {
        _selectedModel = _currentChat!.modelId;
        _visibility = _currentChat!.visibility;
      }

      _fetchSuggestions();
    } catch (e) {
      _setError('加载聊天失败: ${e.toString()}');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 添加待处理的附件
  Future<void> addAttachment(String path) async {
    try {
      final fileInfo = await _fileService.getFileInfo(path);
      final attachment = AttachmentModel(
        id: _generateId(),
        messageId: '', // Will be set when message is created
        fileName: fileInfo['fileName'] as String,
        filePath: path,
        fileSize: fileInfo['fileSize'] as int,
        contentType: fileInfo['contentType'] as String,
        createdAt: DateTime.now(),
        metadata: fileInfo['metadata'] as Map<String, dynamic>?,
      );
      _pendingAttachments.add(attachment);
      notifyListeners();
    } catch (e) {
      _setError('添加附件失败: ${e.toString()}');
    }
  }

  // 移除待处理的附件
  void removeAttachment(AttachmentModel attachment) {
    _pendingAttachments.remove(attachment);
    notifyListeners();
  }

  // 发送消息
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty && _pendingAttachments.isEmpty) {
      return;
    }

    _isGenerating = true;
    _clearError();

    try {
      // 确保有聊天存在
      if (_currentChat == null) {
        await _createNewChat(content);
      }

      final List<AttachmentModel> messageAttachments = List.from(
        _pendingAttachments,
      );

      // 创建用户消息
      final userMessage = MessageModel(
        id: _generateId(),
        chatId: _currentChat!.id,
        role: 'user',
        content: content,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        messageType: 'text',
        metadata:
            messageAttachments.isNotEmpty
                ? {
                  'attachments':
                      messageAttachments.map((a) => a.filePath).toList(),
                }
                : null,
      );

      // 保存用户消息
      await _messageRepository.addMessage(userMessage);
      _messages.add(userMessage);

      // 为附件设置正确的messageId并保存
      for (final attachment in messageAttachments) {
        final finalAttachment = attachment.copyWith(messageId: userMessage.id);
        await _attachmentRepository.addAttachment(finalAttachment);
        _attachments.add(finalAttachment);
      }
      _pendingAttachments.clear();

      // 更新聊天时间
      await _updateChatTimestamp();

      notifyListeners();

      // 生成AI回复
      _generateAIResponse(content, messageAttachments);
    } catch (e) {
      _setError('发送消息失败: ${e.toString()}');
    } finally {
      // Note: _setGenerating is now handled by the stream
    }
  }

  // 生成AI回复 (流式处理)
  void _generateAIResponse(
    String userContent,
    List<AttachmentModel> attachments,
  ) {
    _setGenerating(true);
    _clearError();

    final aiMessage = MessageModel(
      id: _generateId(),
      chatId: _currentChat!.id,
      role: 'assistant',
      content: '', // Start with empty content
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      messageType: 'text',
      metadata: null,
    );
    _messages.add(aiMessage);
    streamingMessage.value = aiMessage;
    notifyListeners();

    try {
      final stream = _llmService.generateStreamResponse(
        userContent,
        attachments,
      );
      _streamSubscription = stream.listen(
        (chunk) {
          streamingMessage.value = streamingMessage.value?.copyWith(
            content: (streamingMessage.value?.content ?? '') + chunk,
            updatedAt: DateTime.now(),
          );
        },
        onDone: () async {
          final finalMessage = streamingMessage.value;
          if (finalMessage != null) {
            final lastMessageIndex = _messages.length - 1;
            _messages[lastMessageIndex] = finalMessage;
            await _messageRepository.addMessage(finalMessage);
          }
          streamingMessage.value = null;
          await _updateChatTimestamp();
          _setGenerating(false);
          _fetchSuggestions(); // Fetch suggestions after response is complete
          notifyListeners();
        },
        onError: (e) {
          final errorMessage =
              '${streamingMessage.value?.content ?? ''}\n\nError: ${e.toString()}';
          final finalMessage = streamingMessage.value?.copyWith(
            content: errorMessage,
          );

          if (finalMessage != null) {
            final lastMessageIndex = _messages.length - 1;
            _messages[lastMessageIndex] = finalMessage;
          }
          streamingMessage.value = null;
          _setError('生成AI回复失败: ${e.toString()}');
          _setGenerating(false);
          notifyListeners();
        },
        cancelOnError: true,
      );
    } catch (e) {
      _setError('生成AI回复失败: ${e.toString()}');
      _setGenerating(false);
    }
  }

  void cancelGeneration() {
    _streamSubscription?.cancel();
    _setGenerating(false);
  }

  // 创建新聊天
  Future<void> _createNewChat(String firstMessage) async {
    final chat = ChatModel(
      id: _generateId(),
      title: _extractTitle(firstMessage),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      visibility: _visibility,
      modelId: _selectedModel,
      metadata: null,
    );

    await _chatRepository.addChat(chat);
    _currentChat = chat;
  }

  // 提取聊天标题
  String _extractTitle(String content) {
    final trimmed = content.trim();
    if (trimmed.isEmpty) return '新聊天';

    final words = trimmed.split(' ');
    if (words.length <= 6) return trimmed;

    return '${words.take(6).join(' ')}...';
  }

  // 投票功能
  Future<void> voteMessage(String messageId, bool isUpvoted) async {
    try {
      await _voteRepository.upsertVote(messageId, isUpvoted);
      final updatedVote = await _voteRepository.getVoteByMessageId(messageId);
      if (updatedVote != null) {
        _votes[messageId] = updatedVote;
        notifyListeners();
      }
    } catch (e) {
      _setError('投票失败: ${e.toString()}');
    }
  }

  // 编辑消息
  Future<void> editMessage(String messageId, String newContent) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index == -1) return;

    final originalMessage = _messages[index];
    final updatedMessage = originalMessage.copyWith(
      content: newContent,
      updatedAt: DateTime.now(),
    );

    _messages[index] = updatedMessage;
    notifyListeners();

    try {
      await _messageRepository.updateMessage(updatedMessage);
    } catch (e) {
      _setError('编辑消息失败: ${e.toString()}');
      _messages[index] = originalMessage; // Revert on error
      notifyListeners();
    }
  }

  // 重新生成消息
  Future<void> regenerateMessage(String messageId) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index == -1 || index == 0) return;

    // We assume the message to regenerate is an assistant message,
    // and the one before it is the user's prompt.
    final userMessage = _messages[index - 1];
    if (userMessage.role != 'user') return; // Should not happen

    // Remove the old assistant message and any subsequent messages
    _messages.removeRange(index, _messages.length);
    notifyListeners();

    _setGenerating(true);
    _clearError();
    try {
      final userMessage = _messages.lastWhere(
        (m) => m.role == 'user' && m.id != messageId,
        orElse: () => _messages.firstWhere((m) => m.role == 'user'),
      );

      final attachments = await _attachmentRepository.getAttachmentsByMessageId(
        userMessage.id,
      );

      _messages.removeWhere((m) => m.id == messageId);
      _generateAIResponse(userMessage.content, attachments);
    } catch (e) {
      _setError('重新生成失败: ${e.toString()}');
    } finally {
      _setGenerating(false);
    }
  }

  // 删除消息
  Future<void> deleteMessage(String messageId) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index == -1) return;

    _messages.removeAt(index);
    notifyListeners();

    try {
      await _messageRepository.deleteMessage(messageId);
    } catch (e) {
      _setError('删除消息失败: ${e.toString()}');
      // Optionally, re-insert the message to revert UI change on error
    }
  }

  // 设置模型
  void setModel(String modelId) {
    _selectedModel = modelId;
    notifyListeners();

    // 更新当前聊天的模型设置
    if (_currentChat != null) {
      _updateChatModel(modelId);
    }
  }

  // 更新聊天模型
  Future<void> _updateChatModel(String modelId) async {
    try {
      final updatedChat = _currentChat!.copyWith(
        modelId: modelId,
        updatedAt: DateTime.now(),
      );
      await _chatRepository.updateChat(updatedChat);
      _currentChat = updatedChat;
    } catch (e) {
      print('更新聊天模型失败: $e');
    }
  }

  // 设置可见性
  Future<void> setVisibility(String visibility) async {
    _visibility = visibility;
    notifyListeners();

    if (_currentChat != null) {
      try {
        final updatedChat = _currentChat!.copyWith(
          visibility: visibility,
          updatedAt: DateTime.now(),
        );
        await _chatRepository.updateChat(updatedChat);
        _currentChat = updatedChat;
      } catch (e) {
        _setError('更新可见性失败: ${e.toString()}');
      }
    }
  }

  // 更新聊天时间戳
  Future<void> _updateChatTimestamp() async {
    if (_currentChat == null) return;

    try {
      final updatedChat = _currentChat!.copyWith(updatedAt: DateTime.now());
      await _chatRepository.updateChat(updatedChat);
      _currentChat = updatedChat;
    } catch (e) {
      print('更新聊天时间戳失败: $e');
    }
  }

  // 清空对话
  Future<void> clearChat() async {
    if (_currentChat == null) return;
    _setLoading(true);
    _clearError();
    try {
      await _attachmentRepository.deleteAttachmentsByChatId(_currentChat!.id);
      await _messageRepository.deleteMessagesByChatId(_currentChat!.id);
      _messages.clear();
      _votes.clear();
      _attachments.clear();
    } catch (e) {
      _setError('清空聊天失败: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // 获取建议
  void _fetchSuggestions() {
    // TODO: Replace with actual suggestion generation logic
    _suggestions = ['用Flutter写一个计数器应用', '今天天气怎么样？', '给我讲个笑话'];
    notifyListeners();
  }

  String _generateId() {
    return 'id_${DateTime.now().millisecondsSinceEpoch}_${UniqueKey().toString()}';
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    streamingMessage.dispose();
    super.dispose();
  }
}
