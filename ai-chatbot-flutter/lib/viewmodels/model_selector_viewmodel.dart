import 'package:flutter/material.dart';

// Represents a single AI model
class AIModel {
  final String id;
  final String name;
  final String description;

  AIModel({required this.id, required this.name, required this.description});
}

class ModelSelectorViewModel extends ChangeNotifier {
  // TODO: Replace with models from a service or configuration
  final List<AIModel> _availableModels = [
    AIModel(id: 'gpt-4', name: 'GPT-4', description: 'The most powerful model'),
    AIModel(
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and reliable'),
    AIModel(
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        description: 'Advanced reasoning'),
  ];

  late AIModel _selectedModel;

  ModelSelectorViewModel() {
    _selectedModel = _availableModels.first;
  }

  List<AIModel> get availableModels => _availableModels;
  AIModel get selectedModel => _selectedModel;

  void selectModel(String modelId) {
    final newModel = _availableModels.firstWhere(
      (model) => model.id == modelId,
      orElse: () => _selectedModel,
    );

    if (newModel.id != _selectedModel.id) {
      _selectedModel = newModel;
      notifyListeners();
    }
  }
}
