import 'dart:async';
import 'package:chatui/models/suggestion_model.dart';
import 'package:chatui/repositories/document_repository.dart';
import 'package:chatui/repositories/suggestion_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/document_model.dart';
import '../services/database_service.dart';

class ArtifactViewModel extends ChangeNotifier {
  final IDocumentRepository _documentRepository;
  final ISuggestionRepository _suggestionRepository;

  DocumentModel? _currentDocument;
  List<DocumentModel> _versions = [];
  List<SuggestionModel> _suggestions = [];
  bool _isLoading = false;
  String? _error;

  DocumentModel? get currentDocument => _currentDocument;
  List<DocumentModel> get versions => _versions;
  List<SuggestionModel> get suggestions => _suggestions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  ArtifactViewModel({
    required IDocumentRepository documentRepository,
    required ISuggestionRepository suggestionRepository,
  }) : _documentRepository = documentRepository,
       _suggestionRepository = suggestionRepository;

  Future<void> loadDocument(String documentId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      _currentDocument = await _documentRepository.getDocumentById(documentId);
      if (_currentDocument != null) {
        _versions = await _documentRepository.getDocumentVersions(
          _currentDocument!.id,
        );
        _suggestions = await _suggestionRepository.getSuggestionsByDocument(
          _currentDocument!.id,
        );
      }
    } catch (e) {
      _error = 'Failed to load document: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void updateContent(String newContent) {
    if (_currentDocument != null) {
      _currentDocument = _currentDocument!.copyWith(content: newContent);
      notifyListeners();
    }
  }

  Future<void> saveCurrentDocument() async {
    if (_currentDocument == null) return;
    _isLoading = true;
    notifyListeners();
    try {
      await _documentRepository.updateDocument(_currentDocument!);
    } catch (e) {
      _error = 'Failed to save document: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> createNewDocument(String title, DocumentKind kind) async {
    _isLoading = true;
    notifyListeners();
    final newDoc = DocumentModel(
      id: Uuid().v4(),
      title: title,
      content: '',
      kind: kind,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    try {
      await _documentRepository.insertDocument(newDoc);
      _currentDocument = newDoc;
      _versions = [newDoc];
      _suggestions = [];
    } catch (e) {
      _error = 'Failed to create document: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
