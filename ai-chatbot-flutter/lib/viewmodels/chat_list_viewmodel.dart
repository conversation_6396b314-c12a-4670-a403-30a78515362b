import 'dart:async';

import 'package:chatui/models/chat_model.dart';
import 'package:chatui/repositories/chat_repository.dart';
import 'package:chatui/services/database_service.dart';
import 'package:chatui/utils/date_utils.dart';
import 'package:chatui/views/dialogs/delete_confirm_dialog.dart';
import 'package:chatui/views/dialogs/rename_chat_dialog.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart' hide DateUtils;

class ChatListViewModel extends ChangeNotifier {
  final IChatRepository _chatRepository = SqliteChatRepository(
    DatabaseService(),
  );

  // 状态变量
  final List<ChatModel> _allChats = [];
  List<ChatModel> _filteredChats = [];
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  String? _selectedChatId;

  // 分页相关
  static const int _pageSize = 20;
  int _currentPage = 1;
  bool _hasMoreChats = true;
  bool _isLoadingMore = false;

  // Getters
  List<ChatModel> get chats {
    if (_searchQuery.isEmpty) {
      return _allChats;
    }
    return _allChats
        .where(
          (chat) =>
              chat.title.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();
  }

  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  String? get selectedChatId => _selectedChatId;
  bool get hasMoreChats => _hasMoreChats;

  // 公共方法，用于UI清除错误
  void clearError() {
    _error = null;
  }

  // 内部状态更新方法
  void _setError(String message) {
    _error = message;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
    }
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  // 分组显示聊天
  Map<String, List<ChatModel>> get groupedChats {
    final Map<String, List<ChatModel>> groups = {
      'Today': [],
      'Yesterday': [],
      'Last 7 days': [],
      'Last 30 days': [],
      'Older': [],
    };

    for (final chat in _filteredChats) {
      final groupName = DateUtils.groupChatByDate(chat.updatedAt);
      if (groups.containsKey(groupName)) {
        groups[groupName]!.add(chat);
      }
    }

    // 移除空分组
    groups.removeWhere((key, value) => value.isEmpty);
    return groups;
  }

  // 加载聊天列表
  Future<void> loadChats({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreChats = true;
      _allChats.clear();
      _filteredChats.clear();
    }

    if (_isLoading || !_hasMoreChats) return;

    _setLoading(true);
    _clearError();

    try {
      final newChats = await _chatRepository.getChats(
        limit: _pageSize,
        offset: (_currentPage - 1) * _pageSize,
      );

      if (newChats.length < _pageSize) {
        _hasMoreChats = false;
      }

      _allChats.addAll(newChats);
      _currentPage++;
      _applySearch();
    } catch (e) {
      _setError('加载聊天列表失败: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // 加载更多聊天
  Future<void> loadMoreChats() async {
    if (_isLoadingMore || !_hasMoreChats) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final newChats = await _chatRepository.getChats(
        limit: _pageSize,
        offset: (_currentPage - 1) * _pageSize,
      );

      if (newChats.length < _pageSize) {
        _hasMoreChats = false;
      }

      _allChats.addAll(newChats);
      _currentPage++;
      _applySearch();
    } catch (e) {
      _setError('加载更多聊天失败: ${e.toString()}');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // 搜索聊天
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applySearch();
  }

  // 应用搜索过滤
  void _applySearch() {
    if (_searchQuery.isEmpty) {
      _filteredChats = List.from(_allChats);
    } else {
      final lowerQuery = _searchQuery.toLowerCase();
      _filteredChats =
          _allChats.where((chat) {
            return chat.title.toLowerCase().contains(lowerQuery);
          }).toList();
    }
    notifyListeners();
  }

  // 选择聊天
  void selectChat(String? chatId) {
    _selectedChatId = chatId;
    notifyListeners();
  }

  // 创建新聊天
  Future<ChatModel> createNewChat({String? title}) async {
    try {
      final chat = ChatModel(
        id: _generateId(),
        title: title ?? '新聊天',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        visibility: 'private',
        modelId: 'gpt-4',
        metadata: null,
      );

      await _chatRepository.addChat(chat);

      // 将新聊天添加到列表开头
      _allChats.insert(0, chat);
      _applySearch();

      return chat;
    } catch (e) {
      _setError('创建聊天失败: ${e.toString()}');
      rethrow;
    }
  }

  // 删除聊天
  Future<void> deleteChat(BuildContext context, String chatId) async {
    final confirmed = await showDeleteConfirmDialog(context);
    if (confirmed != true) {
      return;
    }

    try {
      await _chatRepository.deleteChat(chatId);

      // 从本地列表中移除
      _allChats.removeWhere((chat) => chat.id == chatId);
      _applySearch();

      // 如果删除的是当前选中的聊天，清除选择
      if (_selectedChatId == chatId) {
        _selectedChatId = null;
      }
      notifyListeners(); // Notify after potential selection change
    } catch (e) {
      _setError('删除聊天失败: ${e.toString()}');
    }
  }

  // 重命名聊天
  Future<void> renameChat(BuildContext context, String chatId) async {
    final chat = _allChats.firstWhereOrNull((c) => c.id == chatId);
    if (chat == null) return;

    final newTitle = await showRenameChatDialog(context, chat.title);
    if (newTitle != null && newTitle.isNotEmpty && newTitle != chat.title) {
      await updateChatTitle(chatId, newTitle);
    }
  }

  // 更新聊天标题
  Future<void> updateChatTitle(String chatId, String newTitle) async {
    try {
      final chatIndex = _allChats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex == -1) return;

      final updatedChat = _allChats[chatIndex].copyWith(
        title: newTitle,
        updatedAt: DateTime.now(),
      );

      await _chatRepository.updateChat(updatedChat);
      _allChats[chatIndex] = updatedChat;
      _applySearch();
    } catch (e) {
      _setError('更新聊天标题失败: ${e.toString()}');
    }
  }

  // 更新聊天可见性
  Future<void> updateChatVisibility(String chatId, String visibility) async {
    try {
      final chatIndex = _allChats.indexWhere((chat) => chat.id == chatId);
      if (chatIndex == -1) return;

      final updatedChat = _allChats[chatIndex].copyWith(
        visibility: visibility,
        updatedAt: DateTime.now(),
      );

      await _chatRepository.updateChat(updatedChat);
      _allChats[chatIndex] = updatedChat;
      _applySearch();
    } catch (e) {
      _setError('更新聊天可见性失败: ${e.toString()}');
    }
  }

  // 更新聊天时间戳（当有新消息时）
  void updateChatTimestamp(String chatId) {
    final chatIndex = _allChats.indexWhere((chat) => chat.id == chatId);
    if (chatIndex == -1) return;

    final updatedChat = _allChats[chatIndex].copyWith(
      updatedAt: DateTime.now(),
    );

    _allChats[chatIndex] = updatedChat;

    // 重新排序：将更新的聊天移到前面
    _allChats.removeAt(chatIndex);
    _allChats.insert(0, updatedChat);

    _applySearch();
  }

  // 获取特定聊天
  ChatModel? getChatById(String chatId) {
    try {
      return _allChats.firstWhere((chat) => chat.id == chatId);
    } catch (e) {
      return null;
    }
  }

  // 获取最近的聊天
  List<ChatModel> getRecentChats({int limit = 5}) {
    final recentChats = List<ChatModel>.from(_allChats);
    recentChats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    return recentChats.take(limit).toList();
  }

  // 按可见性过滤聊天
  List<ChatModel> getChatsByVisibility(String visibility) {
    return _allChats.where((chat) => chat.visibility == visibility).toList();
  }

  // 按模型过滤聊天
  List<ChatModel> getChatsByModel(String modelId) {
    return _allChats.where((chat) => chat.modelId == modelId).toList();
  }

  // 清除搜索
  void clearSearch() {
    _searchQuery = '';
    _applySearch();
  }

  // 刷新聊天列表
  Future<void> refresh() async {
    await loadChats(refresh: true);
  }

  // 批量删除聊天
  Future<void> deleteChats(List<String> chatIds) async {
    try {
      for (final chatId in chatIds) {
        await _chatRepository.deleteChat(chatId);
      }

      // 从本地列表中移除
      _allChats.removeWhere((chat) => chatIds.contains(chat.id));
      _applySearch();

      // 如果删除的包含当前选中的聊天，清除选择
      if (_selectedChatId != null && chatIds.contains(_selectedChatId)) {
        _selectedChatId = null;
      }
    } catch (e) {
      _setError('批量删除聊天失败: ${e.toString()}');
    }
  }

  // 获取聊天统计信息
  Map<String, int> getChatStats() {
    return {
      'total': _allChats.length,
      'private': _allChats.where((chat) => chat.visibility == 'private').length,
      'public': _allChats.where((chat) => chat.visibility == 'public').length,
      'today': groupedChats['Today']?.length ?? 0,
      'yesterday': groupedChats['Yesterday']?.length ?? 0,
      'thisWeek': groupedChats['Last 7 days']?.length ?? 0,
      'thisMonth': groupedChats['Last 30 days']?.length ?? 0,
      'older': groupedChats['Older']?.length ?? 0,
    };
  }

  String _generateId() {
    return 'chat_${DateTime.now().millisecondsSinceEpoch}_${UniqueKey().toString()}';
  }

  // 初始化方法
  Future<void> initialize() async {
    await loadChats(refresh: true);
  }
}
