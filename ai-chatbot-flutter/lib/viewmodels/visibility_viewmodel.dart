import 'package:flutter/material.dart';

enum ChatVisibility {
  private,
  public,
}

class VisibilityViewModel extends ChangeNotifier {
  ChatVisibility _currentVisibility = ChatVisibility.private;

  ChatVisibility get currentVisibility => _currentVisibility;

  void setVisibility(ChatVisibility newVisibility) {
    if (_currentVisibility != newVisibility) {
      _currentVisibility = newVisibility;
      notifyListeners();
      // Here you would also typically call a service to persist this change
      // e.g., _databaseService.updateChatVisibility(chatId, newVisibility);
    }
  }

  String get visibilityDescription {
    switch (_currentVisibility) {
      case ChatVisibility.private:
        return 'Only you can see this chat.';
      case ChatVisibility.public:
        return 'This chat is public and shareable.';
      default:
        return '';
    }
  }
}
