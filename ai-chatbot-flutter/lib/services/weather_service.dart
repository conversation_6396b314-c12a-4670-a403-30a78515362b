import 'dart:math';

/// 天气查询服务
/// 提供天气信息查询功能（当前为占位符实现）
class WeatherService {
  static final WeatherService _instance = WeatherService._internal();
  factory WeatherService() => _instance;
  WeatherService._internal();

  final Random _random = Random();

  /// 根据城市名称获取天气信息
  Future<WeatherData> getWeatherByCity(String cityName) async {
    try {
      // 占位符实现 - 实际应该调用真实的天气API
      await Future.delayed(Duration(seconds: 1));

      // 模拟API调用（注释掉的代码是实际API调用示例）
      /*
      final url = Uri.parse('$_baseUrl/weather?q=$cityName&appid=$_apiKey&units=metric');
      final response = await http.get(url).timeout(_timeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return WeatherData.fromJson(data);
      } else {
        throw WeatherException('Failed to fetch weather data: ${response.statusCode}');
      }
      */

      // 模拟返回数据
      return _generateMockWeatherData(cityName);
    } catch (e) {
      throw WeatherException('Failed to get weather for $cityName: $e');
    }
  }

  /// 根据坐标获取天气信息
  Future<WeatherData> getWeatherByCoordinates(
      double latitude, double longitude) async {
    try {
      // 占位符实现 - 实际应该调用真实的天气API
      await Future.delayed(Duration(seconds: 1));

      // 模拟API调用（注释掉的代码是实际API调用示例）
      /*
      final url = Uri.parse('$_baseUrl/weather?lat=$latitude&lon=$longitude&appid=$_apiKey&units=metric');
      final response = await http.get(url).timeout(_timeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return WeatherData.fromJson(data);
      } else {
        throw WeatherException('Failed to fetch weather data: ${response.statusCode}');
      }
      */

      // 模拟返回数据
      return _generateMockWeatherData('Location($latitude, $longitude)');
    } catch (e) {
      throw WeatherException('Failed to get weather for coordinates: $e');
    }
  }

  /// 获取天气预报
  Future<List<WeatherData>> getWeatherForecast(String cityName,
      {int days = 5}) async {
    try {
      // 占位符实现 - 实际应该调用真实的天气API
      await Future.delayed(Duration(seconds: 1));

      // 模拟API调用（注释掉的代码是实际API调用示例）
      /*
      final url = Uri.parse('$_baseUrl/forecast?q=$cityName&appid=$_apiKey&units=metric&cnt=${days * 8}');
      final response = await http.get(url).timeout(_timeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> list = data['list'];
        return list.map((item) => WeatherData.fromJson(item)).toList();
      } else {
        throw WeatherException('Failed to fetch weather forecast: ${response.statusCode}');
      }
      */

      // 模拟返回数据
      return _generateMockForecastData(cityName, days);
    } catch (e) {
      throw WeatherException(
          'Failed to get weather forecast for $cityName: $e');
    }
  }

  /// 搜索城市
  Future<List<CityData>> searchCities(String query) async {
    try {
      // 占位符实现 - 实际应该调用真实的地理编码API
      await Future.delayed(Duration(milliseconds: 500));

      // 模拟API调用（注释掉的代码是实际API调用示例）
      /*
      final url = Uri.parse('http://api.openweathermap.org/geo/1.0/direct?q=$query&limit=5&appid=$_apiKey');
      final response = await http.get(url).timeout(_timeout);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => CityData.fromJson(item)).toList();
      } else {
        throw WeatherException('Failed to search cities: ${response.statusCode}');
      }
      */

      // 模拟返回数据
      return _generateMockCityData(query);
    } catch (e) {
      throw WeatherException('Failed to search cities: $e');
    }
  }

  /// 获取空气质量信息
  Future<AirQualityData> getAirQuality(
      double latitude, double longitude) async {
    try {
      // 占位符实现 - 实际应该调用真实的空气质量API
      await Future.delayed(Duration(milliseconds: 800));

      // 模拟API调用（注释掉的代码是实际API调用示例）
      /*
      final url = Uri.parse('$_baseUrl/air_pollution?lat=$latitude&lon=$longitude&appid=$_apiKey');
      final response = await http.get(url).timeout(_timeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AirQualityData.fromJson(data);
      } else {
        throw WeatherException('Failed to fetch air quality data: ${response.statusCode}');
      }
      */

      // 模拟返回数据
      return _generateMockAirQualityData();
    } catch (e) {
      throw WeatherException('Failed to get air quality: $e');
    }
  }

  /// 生成模拟天气数据
  WeatherData _generateMockWeatherData(String locationName) {
    final conditions = [
      'Clear',
      'Cloudy',
      'Partly Cloudy',
      'Rainy',
      'Snowy',
      'Stormy'
    ];
    final icons = ['☀️', '☁️', '⛅', '🌧️', '❄️', '⛈️'];
    final index = _random.nextInt(conditions.length);

    return WeatherData(
      location: locationName,
      temperature: _random.nextDouble() * 40 - 10, // -10 to 30°C
      feelsLike: _random.nextDouble() * 40 - 10,
      humidity: _random.nextInt(100),
      pressure: 1000 + _random.nextInt(100),
      windSpeed: _random.nextDouble() * 20,
      windDirection: _random.nextInt(360),
      visibility: _random.nextDouble() * 15 + 5,
      uvIndex: _random.nextInt(11),
      condition: conditions[index],
      description: _generateWeatherDescription(conditions[index]),
      icon: icons[index],
      timestamp: DateTime.now(),
    );
  }

  /// 生成模拟预报数据
  List<WeatherData> _generateMockForecastData(String cityName, int days) {
    final forecast = <WeatherData>[];
    final now = DateTime.now();

    for (int i = 0; i < days; i++) {
      final date = now.add(Duration(days: i));
      final weatherData = _generateMockWeatherData(cityName);

      forecast.add(weatherData.copyWith(
        timestamp: date,
        temperature: weatherData.temperature + _random.nextDouble() * 10 - 5,
      ));
    }

    return forecast;
  }

  /// 生成模拟城市数据
  List<CityData> _generateMockCityData(String query) {
    final cities = <CityData>[];
    final sampleCities = [
      {'name': 'Beijing', 'country': 'China', 'lat': 39.9042, 'lon': 116.4074},
      {'name': 'Shanghai', 'country': 'China', 'lat': 31.2304, 'lon': 121.4737},
      {'name': 'New York', 'country': 'USA', 'lat': 40.7128, 'lon': -74.0060},
      {'name': 'London', 'country': 'UK', 'lat': 51.5074, 'lon': -0.1278},
      {'name': 'Tokyo', 'country': 'Japan', 'lat': 35.6762, 'lon': 139.6503},
      {'name': 'Paris', 'country': 'France', 'lat': 48.8566, 'lon': 2.3522},
    ];

    // 模拟搜索结果
    final filteredCities = sampleCities
        .where((city) => city['name']!
            .toString()
            .toLowerCase()
            .contains(query.toLowerCase()))
        .toList();

    for (final city in filteredCities) {
      cities.add(CityData(
        name: city['name']! as String,
        country: city['country']! as String,
        latitude: city['lat']! as double,
        longitude: city['lon']! as double,
      ));
    }

    return cities;
  }

  /// 生成模拟空气质量数据
  AirQualityData _generateMockAirQualityData() {
    return AirQualityData(
      aqi: _random.nextInt(500) + 1,
      pm25: _random.nextDouble() * 100,
      pm10: _random.nextDouble() * 150,
      o3: _random.nextDouble() * 200,
      no2: _random.nextDouble() * 100,
      so2: _random.nextDouble() * 50,
      co: _random.nextDouble() * 10,
      timestamp: DateTime.now(),
    );
  }

  /// 生成天气描述
  String _generateWeatherDescription(String condition) {
    switch (condition) {
      case 'Clear':
        return 'Clear sky with plenty of sunshine';
      case 'Cloudy':
        return 'Overcast with thick clouds';
      case 'Partly Cloudy':
        return 'Partly cloudy with some sunshine';
      case 'Rainy':
        return 'Light to moderate rain expected';
      case 'Snowy':
        return 'Snow showers with cold temperatures';
      case 'Stormy':
        return 'Thunderstorms with heavy rain';
      default:
        return 'Weather conditions vary';
    }
  }
}

/// 天气数据模型
class WeatherData {
  final String location;
  final double temperature;
  final double feelsLike;
  final int humidity;
  final int pressure;
  final double windSpeed;
  final int windDirection;
  final double visibility;
  final int uvIndex;
  final String condition;
  final String description;
  final String icon;
  final DateTime timestamp;

  const WeatherData({
    required this.location,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.pressure,
    required this.windSpeed,
    required this.windDirection,
    required this.visibility,
    required this.uvIndex,
    required this.condition,
    required this.description,
    required this.icon,
    required this.timestamp,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      location: json['name'] ?? 'Unknown',
      temperature: (json['main']['temp'] as num).toDouble(),
      feelsLike: (json['main']['feels_like'] as num).toDouble(),
      humidity: json['main']['humidity'] as int,
      pressure: json['main']['pressure'] as int,
      windSpeed: (json['wind']['speed'] as num).toDouble(),
      windDirection: json['wind']['deg'] as int,
      visibility: (json['visibility'] as num).toDouble() / 1000,
      uvIndex: json['uvi'] as int? ?? 0,
      condition: json['weather'][0]['main'] as String,
      description: json['weather'][0]['description'] as String,
      icon: json['weather'][0]['icon'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['dt'] * 1000),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'location': location,
      'temperature': temperature,
      'feelsLike': feelsLike,
      'humidity': humidity,
      'pressure': pressure,
      'windSpeed': windSpeed,
      'windDirection': windDirection,
      'visibility': visibility,
      'uvIndex': uvIndex,
      'condition': condition,
      'description': description,
      'icon': icon,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  WeatherData copyWith({
    String? location,
    double? temperature,
    double? feelsLike,
    int? humidity,
    int? pressure,
    double? windSpeed,
    int? windDirection,
    double? visibility,
    int? uvIndex,
    String? condition,
    String? description,
    String? icon,
    DateTime? timestamp,
  }) {
    return WeatherData(
      location: location ?? this.location,
      temperature: temperature ?? this.temperature,
      feelsLike: feelsLike ?? this.feelsLike,
      humidity: humidity ?? this.humidity,
      pressure: pressure ?? this.pressure,
      windSpeed: windSpeed ?? this.windSpeed,
      windDirection: windDirection ?? this.windDirection,
      visibility: visibility ?? this.visibility,
      uvIndex: uvIndex ?? this.uvIndex,
      condition: condition ?? this.condition,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  String get temperatureString => '${temperature.round()}°C';
  String get feelsLikeString => '${feelsLike.round()}°C';
  String get windSpeedString => '${windSpeed.toStringAsFixed(1)} m/s';
  String get visibilityString => '${visibility.toStringAsFixed(1)} km';
  String get pressureString => '$pressure hPa';
  String get humidityString => '$humidity%';
  String get uvIndexString => uvIndex.toString();

  String get windDirectionString {
    if (windDirection >= 337.5 || windDirection < 22.5) return 'N';
    if (windDirection >= 22.5 && windDirection < 67.5) return 'NE';
    if (windDirection >= 67.5 && windDirection < 112.5) return 'E';
    if (windDirection >= 112.5 && windDirection < 157.5) return 'SE';
    if (windDirection >= 157.5 && windDirection < 202.5) return 'S';
    if (windDirection >= 202.5 && windDirection < 247.5) return 'SW';
    if (windDirection >= 247.5 && windDirection < 292.5) return 'W';
    if (windDirection >= 292.5 && windDirection < 337.5) return 'NW';
    return 'N';
  }
}

/// 城市数据模型
class CityData {
  final String name;
  final String country;
  final double latitude;
  final double longitude;

  const CityData({
    required this.name,
    required this.country,
    required this.latitude,
    required this.longitude,
  });

  factory CityData.fromJson(Map<String, dynamic> json) {
    return CityData(
      name: json['name'] as String,
      country: json['country'] as String,
      latitude: (json['lat'] as num).toDouble(),
      longitude: (json['lon'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'country': country,
      'lat': latitude,
      'lon': longitude,
    };
  }

  String get displayName => '$name, $country';
}

/// 空气质量数据模型
class AirQualityData {
  final int aqi;
  final double pm25;
  final double pm10;
  final double o3;
  final double no2;
  final double so2;
  final double co;
  final DateTime timestamp;

  const AirQualityData({
    required this.aqi,
    required this.pm25,
    required this.pm10,
    required this.o3,
    required this.no2,
    required this.so2,
    required this.co,
    required this.timestamp,
  });

  factory AirQualityData.fromJson(Map<String, dynamic> json) {
    final components = json['list'][0]['components'];
    return AirQualityData(
      aqi: json['list'][0]['main']['aqi'] as int,
      pm25: (components['pm2_5'] as num).toDouble(),
      pm10: (components['pm10'] as num).toDouble(),
      o3: (components['o3'] as num).toDouble(),
      no2: (components['no2'] as num).toDouble(),
      so2: (components['so2'] as num).toDouble(),
      co: (components['co'] as num).toDouble(),
      timestamp:
          DateTime.fromMillisecondsSinceEpoch(json['list'][0]['dt'] * 1000),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'aqi': aqi,
      'pm25': pm25,
      'pm10': pm10,
      'o3': o3,
      'no2': no2,
      'so2': so2,
      'co': co,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  String get aqiLevel {
    if (aqi <= 50) return 'Good';
    if (aqi <= 100) return 'Moderate';
    if (aqi <= 150) return 'Unhealthy for Sensitive Groups';
    if (aqi <= 200) return 'Unhealthy';
    if (aqi <= 300) return 'Very Unhealthy';
    return 'Hazardous';
  }

  String get aqiDescription {
    switch (aqiLevel) {
      case 'Good':
        return 'Air quality is satisfactory, and air pollution poses little or no risk.';
      case 'Moderate':
        return 'Air quality is acceptable. However, there may be a risk for some people, particularly those who are unusually sensitive to air pollution.';
      case 'Unhealthy for Sensitive Groups':
        return 'Members of sensitive groups may experience health effects. The general public is less likely to be affected.';
      case 'Unhealthy':
        return 'Some members of the general public may experience health effects; members of sensitive groups may experience more serious health effects.';
      case 'Very Unhealthy':
        return 'Health alert: The risk of health effects is increased for everyone.';
      case 'Hazardous':
        return 'Health warning of emergency conditions: everyone is more likely to be affected.';
      default:
        return 'Air quality information not available.';
    }
  }
}

/// 天气服务异常
class WeatherException implements Exception {
  final String message;
  final dynamic cause;

  const WeatherException(this.message, [this.cause]);

  @override
  String toString() =>
      'WeatherException: $message${cause != null ? ' (cause: $cause)' : ''}';
}
