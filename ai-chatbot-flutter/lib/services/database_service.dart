import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../models/attachment_model.dart';
import '../models/vote_model.dart';
import '../models/document_model.dart';
import '../models/suggestion_model.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'chatbot.db';
  static const int _databaseVersion = 1;

  // 单例模式
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String databasesPath = await getDatabasesPath();
    String path = join(databasesPath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // 创建聊天表
    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        visibility TEXT DEFAULT 'private' CHECK(visibility IN ('private', 'public')),
        model_id TEXT DEFAULT 'gpt-4',
        metadata TEXT
      )
    ''');

    // 创建消息表
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        chat_id TEXT NOT NULL,
        role TEXT NOT NULL CHECK(role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        message_type TEXT DEFAULT 'text',
        metadata TEXT,
        FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
      )
    ''');

    // 创建附件表
    await db.execute('''
      CREATE TABLE attachments (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        content_type TEXT NOT NULL,
        created_at TEXT NOT NULL,
        metadata TEXT,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
      )
    ''');

    // 创建投票表
    await db.execute('''
      CREATE TABLE votes (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        is_upvoted INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
      )
    ''');

    // 创建文档表（Artifacts）
    await db.execute('''
      CREATE TABLE documents (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        kind TEXT NOT NULL CHECK(kind IN ('text', 'code', 'image', 'sheet')),
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        version INTEGER DEFAULT 1,
        parent_id TEXT,
        metadata TEXT
      )
    ''');

    // 创建建议表
    await db.execute('''
      CREATE TABLE suggestions (
        id TEXT PRIMARY KEY,
        document_id TEXT NOT NULL,
        description TEXT NOT NULL,
        selection_start INTEGER,
        selection_end INTEGER,
        created_at TEXT NOT NULL,
        is_applied INTEGER DEFAULT 0,
        applied_content TEXT,
        FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
      )
    ''');

    // 创建设置表
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // 创建索引
    await _createIndexes(db);
  }

  Future<void> _createIndexes(Database db) async {
    await db.execute('CREATE INDEX idx_messages_chat_id ON messages(chat_id)');
    await db.execute(
      'CREATE INDEX idx_messages_created_at ON messages(created_at)',
    );
    await db.execute(
      'CREATE INDEX idx_attachments_message_id ON attachments(message_id)',
    );
    await db.execute('CREATE INDEX idx_votes_message_id ON votes(message_id)');
    await db.execute(
      'CREATE INDEX idx_suggestions_document_id ON suggestions(document_id)',
    );
    await db.execute('CREATE INDEX idx_chats_updated_at ON chats(updated_at)');
    await db.execute(
      'CREATE INDEX idx_documents_created_at ON documents(created_at)',
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // 数据库升级逻辑
    if (oldVersion < newVersion) {
      // 根据版本差异执行相应的升级操作
    }
  }

  // ============ 聊天相关操作 (已迁移至 ChatRepository) ============
  // insertChat, getAllChats, getChatById, updateChat, deleteChat 等方法已移除。

  Future<void> deleteMessagesByChat(String chatId) async {
    final db = await database;
    await db.delete('messages', where: 'chat_id = ?', whereArgs: [chatId]);
  }

  // ============ 消息相关操作 (已迁移至 MessageRepository) ============
  // insertMessage, getMessagesByChat, updateMessage, deleteMessage 等方法已移除。

  // ============ 附件相关操作 (已迁移至 AttachmentRepository) ============
  // insertAttachment, getAttachmentsByMessage, getAttachmentsByChat, deleteAttachment 等方法已移除。

  // ============ 投票相关操作 (已迁移至 VoteRepository) ============
  // upsertVote, getVotesByChat, getVoteByMessage 等方法已移除。

  // ============ 文档相关操作 (已迁移至 DocumentRepository) ============
  // insertDocument, getDocumentById, getAllDocuments, updateDocument, deleteDocument 等方法已移除。

  // ============ 建议相关操作 (已迁移至 SuggestionRepository) ============
  // insertSuggestion, getSuggestionsByDocument, updateSuggestion, deleteSuggestion 等方法已移除。

  // ============ 设置相关操作 ============

  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert('settings', {
      'key': key,
      'value': value,
      'updated_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
      limit: 1,
    );
    if (result.isEmpty) return null;
    return result.first['value'] as String;
  }

  Future<Map<String, String>> getAllSettings() async {
    final db = await database;
    final result = await db.query('settings');
    final settings = <String, String>{};
    for (final row in result) {
      settings[row['key'] as String] = row['value'] as String;
    }
    return settings;
  }

  Future<void> updateSetting(String key, String value) async {
    final db = await database;
    await db.update(
      'settings',
      {'value': value, 'updated_at': DateTime.now().toIso8601String()},
      where: 'key = ?',
      whereArgs: [key],
    );
  }

  // ============ 数据库维护操作 ============

  Future<void> clearAllData() async {
    final db = await database;
    await db.transaction((txn) async {
      await txn.delete('suggestions');
      await txn.delete('documents');
      await txn.delete('votes');
      await txn.delete('attachments');
      await txn.delete('messages');
      await txn.delete('chats');
      await txn.delete('settings');
    });
  }

  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  Future<int> getDatabaseSize() async {
    String databasesPath = await getDatabasesPath();
    String path = join(databasesPath, _databaseName);
    File file = File(path);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }
}
