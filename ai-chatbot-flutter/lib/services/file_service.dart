import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';
import '../models/attachment_model.dart';
import '../utils/constants.dart';

/// 文件管理服务
/// 提供文件上传、下载、存储、预览等功能
class FileService {
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;
  FileService._internal();

  final ImagePicker _imagePicker = ImagePicker();
  final Uuid _uuid = Uuid();

  // 应用文档目录
  Directory? _appDocumentsDir;
  Directory? _appCacheDir;

  /// 初始化文件服务
  Future<void> initialize() async {
    try {
      _appDocumentsDir = await getApplicationDocumentsDirectory();
      _appCacheDir = await getApplicationCacheDirectory();

      // 创建必要的子目录
      await _createRequiredDirectories();

      print('FileService initialized successfully');
    } catch (e) {
      throw FileServiceException('Failed to initialize FileService: $e');
    }
  }

  /// 创建必要的目录
  Future<void> _createRequiredDirectories() async {
    final directories = [
      'attachments',
      'images',
      'documents',
      'exports',
      'cache',
      'temp',
    ];

    for (final dir in directories) {
      final path = '${_appDocumentsDir!.path}/$dir';
      final directory = Directory(path);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
    }
  }

  /// 获取应用文档目录
  Directory get documentsDirectory {
    if (_appDocumentsDir == null) {
      throw FileServiceException('FileService not initialized');
    }
    return _appDocumentsDir!;
  }

  /// 获取应用缓存目录
  Directory get cacheDirectory {
    if (_appCacheDir == null) {
      throw FileServiceException('FileService not initialized');
    }
    return _appCacheDir!;
  }

  /// 选择文件
  Future<List<AttachmentModel>> pickFiles({
    FileType fileType = FileType.any,
    bool allowMultiple = true,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowMultiple: allowMultiple,
        allowedExtensions: allowedExtensions,
      );

      if (result == null || result.files.isEmpty) {
        return [];
      }

      final attachments = <AttachmentModel>[];

      for (final file in result.files) {
        if (file.path == null) continue;

        // 检查文件大小
        if (file.size > AppConstants.maxFileSize) {
          throw FileServiceException(
            'File ${file.name} exceeds maximum size limit (${AppConstants.maxFileSize ~/ (1024 * 1024)}MB)',
          );
        }

        // 复制文件到应用目录
        final savedPath = await _saveFile(File(file.path!), file.name);

        final attachment = AttachmentModel(
          id: _uuid.v4(),
          messageId: '', // 稍后设置
          fileName: file.name,
          filePath: savedPath,
          fileSize: file.size,
          contentType: _getContentType(file.name),
          createdAt: DateTime.now(),
        );

        attachments.add(attachment);
      }

      return attachments;
    } catch (e) {
      throw FileServiceException('Failed to pick files: $e');
    }
  }

  /// 选择图片
  Future<List<AttachmentModel>> pickImages({
    ImageSource source = ImageSource.gallery,
    bool allowMultiple = true,
    int? imageQuality,
  }) async {
    try {
      final List<XFile> images;

      if (allowMultiple && source == ImageSource.gallery) {
        final multiImages = await _imagePicker.pickMultiImage(
          imageQuality: imageQuality,
        );
        images = multiImages;
      } else {
        final image = await _imagePicker.pickImage(
          source: source,
          imageQuality: imageQuality,
        );
        images = image != null ? [image] : [];
      }

      if (images.isEmpty) return [];

      final attachments = <AttachmentModel>[];

      for (final image in images) {
        final file = File(image.path);
        final fileSize = await file.length();

        // 检查文件大小
        if (fileSize > AppConstants.maxFileSize) {
          throw FileServiceException(
            'Image ${image.name} exceeds maximum size limit (${AppConstants.maxFileSize ~/ (1024 * 1024)}MB)',
          );
        }

        // 复制文件到应用目录
        final savedPath = await _saveFile(file, image.name);

        final attachment = AttachmentModel(
          id: _uuid.v4(),
          messageId: '', // 稍后设置
          fileName: image.name,
          filePath: savedPath,
          fileSize: fileSize,
          contentType: 'image/${image.path.split('.').last}',
          createdAt: DateTime.now(),
        );

        attachments.add(attachment);
      }

      return attachments;
    } catch (e) {
      throw FileServiceException('Failed to pick images: $e');
    }
  }

  /// 保存文件到应用目录
  Future<String> _saveFile(File sourceFile, String fileName) async {
    try {
      // 生成唯一文件名
      final extension = fileName.split('.').last;
      final uniqueName = '${_uuid.v4()}.$extension';

      // 确定保存目录
      final String subDir = _getSubDirectory(fileName);
      final targetDir = Directory('${_appDocumentsDir!.path}/$subDir');

      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // 复制文件
      final targetPath = '${targetDir.path}/$uniqueName';
      final targetFile = await sourceFile.copy(targetPath);

      return targetFile.path;
    } catch (e) {
      throw FileServiceException('Failed to save file: $e');
    }
  }

  /// 获取文件子目录
  String _getSubDirectory(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    if (AppConstants.imageExtensions.contains(extension)) {
      return 'images';
    } else if (AppConstants.documentExtensions.contains(extension)) {
      return 'documents';
    } else {
      return 'attachments';
    }
  }

  /// 获取内容类型
  String _getContentType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    // 图片类型
    if (AppConstants.imageExtensions.contains(extension)) {
      return 'image/$extension';
    }

    // 文档类型
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      default:
        return 'application/octet-stream';
    }
  }

  /// 删除文件
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Failed to delete file: $e');
      return false;
    }
  }

  /// 获取文件信息
  Future<Map<String, dynamic>> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileServiceException('File not found: $filePath');
      }

      final stat = await file.stat();
      final fileName = file.path.split('/').last;

      return {
        'name': fileName,
        'size': stat.size,
        'modified': stat.modified,
        'type': _getContentType(fileName),
        'isImage': _isImageFile(fileName),
        'isDocument': _isDocumentFile(fileName),
      };
    } catch (e) {
      throw FileServiceException('Failed to get file info: $e');
    }
  }

  /// 检查是否为图片文件
  bool _isImageFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return AppConstants.imageExtensions.contains(extension);
  }

  /// 检查是否为文档文件
  bool _isDocumentFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return AppConstants.documentExtensions.contains(extension);
  }

  /// 读取文件内容
  Future<Uint8List> readFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileServiceException('File not found: $filePath');
      }

      return await file.readAsBytes();
    } catch (e) {
      throw FileServiceException('Failed to read file: $e');
    }
  }

  /// 写入文件内容
  Future<String> writeFile(
    String fileName,
    Uint8List data, {
    String? subDir,
  }) async {
    try {
      final targetDir = subDir != null
          ? Directory('${_appDocumentsDir!.path}/$subDir')
          : Directory('${_appDocumentsDir!.path}/exports');

      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final filePath = '${targetDir.path}/$fileName';
      final file = File(filePath);

      await file.writeAsBytes(data);

      return filePath;
    } catch (e) {
      throw FileServiceException('Failed to write file: $e');
    }
  }

  /// 复制文件
  Future<String> copyFile(
    String sourcePath,
    String targetName, {
    String? subDir,
  }) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw FileServiceException('Source file not found: $sourcePath');
      }

      final targetDir = subDir != null
          ? Directory('${_appDocumentsDir!.path}/$subDir')
          : Directory('${_appDocumentsDir!.path}/exports');

      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final targetPath = '${targetDir.path}/$targetName';
      final targetFile = await sourceFile.copy(targetPath);

      return targetFile.path;
    } catch (e) {
      throw FileServiceException('Failed to copy file: $e');
    }
  }

  /// 获取存储空间信息
  Future<Map<String, int>> getStorageInfo() async {
    try {
      final dir = _appDocumentsDir!;
      int totalSize = 0;
      int fileCount = 0;

      await for (final entity in dir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
          fileCount++;
        }
      }

      return {'totalSize': totalSize, 'fileCount': fileCount};
    } catch (e) {
      throw FileServiceException('Failed to get storage info: $e');
    }
  }

  /// 清理缓存文件
  Future<void> clearCache() async {
    try {
      final cacheDir = Directory('${_appCacheDir!.path}');
      final tempDir = Directory('${_appDocumentsDir!.path}/temp');

      // 清理缓存目录
      if (await cacheDir.exists()) {
        await for (final entity in cacheDir.list()) {
          if (entity is File) {
            await entity.delete();
          } else if (entity is Directory) {
            await entity.delete(recursive: true);
          }
        }
      }

      // 清理临时目录
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
        await tempDir.create();
      }
    } catch (e) {
      throw FileServiceException('Failed to clear cache: $e');
    }
  }

  /// 导出文件
  Future<String> exportFile(String filePath, String exportName) async {
    try {
      final sourceFile = File(filePath);
      if (!await sourceFile.exists()) {
        throw FileServiceException('File not found: $filePath');
      }

      final exportDir = Directory('${_appDocumentsDir!.path}/exports');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      final exportPath = '${exportDir.path}/$exportName';
      final exportFile = await sourceFile.copy(exportPath);

      return exportFile.path;
    } catch (e) {
      throw FileServiceException('Failed to export file: $e');
    }
  }

  /// 检查存储权限
  Future<bool> checkStoragePermission() async {
    try {
      final permission = Permission.storage;
      final status = await permission.status;

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        final result = await permission.request();
        return result.isGranted;
      }

      return false;
    } catch (e) {
      print('Failed to check storage permission: $e');
      return false;
    }
  }
}

/// 文件服务异常
class FileServiceException implements Exception {
  final String message;
  final dynamic cause;

  const FileServiceException(this.message, [this.cause]);

  @override
  String toString() =>
      'FileServiceException: $message${cause != null ? ' (cause: $cause)' : ''}';
}
