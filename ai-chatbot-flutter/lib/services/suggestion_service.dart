import 'dart:math';
import '../models/suggestion_model.dart';
import '../models/document_model.dart';
import '../models/message_model.dart';

/// 智能建议服务
/// 提供基于上下文的智能建议生成功能
class SuggestionService {
  static final SuggestionService _instance = SuggestionService._internal();
  factory SuggestionService() => _instance;
  SuggestionService._internal();

  final Random _random = Random();

  /// 生成聊天建议
  Future<List<String>> generateChatSuggestions({
    List<MessageModel>? messages,
    String? currentInput,
    int maxSuggestions = 3,
  }) async {
    try {
      // 获取上下文
      final context = _buildChatContext(messages, currentInput);

      // 基于上下文生成建议
      final suggestions = await _generateContextualSuggestions(
        context: context,
        type: 'chat',
        maxCount: maxSuggestions,
      );

      return suggestions;
    } catch (e) {
      // 返回默认建议
      return _getDefaultChatSuggestions(maxSuggestions);
    }
  }

  /// 生成文档编辑建议
  Future<List<SuggestionModel>> generateDocumentSuggestions({
    required DocumentModel document,
    String? selectedText,
    int? selectionStart,
    int? selectionEnd,
    int maxSuggestions = 5,
  }) async {
    try {
      final suggestions = <SuggestionModel>[];

      // 根据文档类型生成不同建议
      switch (document.kind) {
        case DocumentKind.text:
          suggestions.addAll(
            await _generateTextSuggestions(
              document,
              selectedText,
              selectionStart,
              selectionEnd,
            ),
          );
          break;
        case DocumentKind.code:
          suggestions.addAll(
            await _generateCodeSuggestions(
              document,
              selectedText,
              selectionStart,
              selectionEnd,
            ),
          );
          break;
        case DocumentKind.image:
          suggestions.addAll(await _generateImageSuggestions(document));
          break;
        case DocumentKind.sheet:
          suggestions.addAll(await _generateSheetSuggestions(document));
          break;
      }

      // 限制建议数量
      if (suggestions.length > maxSuggestions) {
        suggestions.shuffle(_random);
        return suggestions.take(maxSuggestions).toList();
      }

      return suggestions;
    } catch (e) {
      return _getDefaultDocumentSuggestions(
        document.kind.value,
        maxSuggestions,
      );
    }
  }

  /// 生成快速操作建议
  List<String> generateQuickActions({String? context, String? fileType}) {
    final actions = <String>[];

    // 基础操作
    actions.addAll([
      "Help me write a summary",
      "Explain this concept",
      "Generate examples",
      "Find related information",
    ]);

    // 文件类型特定操作
    if (fileType != null) {
      switch (fileType.toLowerCase()) {
        case 'code':
          actions.addAll([
            "Review this code",
            "Optimize performance",
            "Add comments",
            "Find bugs",
            "Generate tests",
          ]);
          break;
        case 'text':
          actions.addAll([
            "Improve grammar",
            "Make it more concise",
            "Translate to another language",
            "Change tone",
          ]);
          break;
        case 'image':
          actions.addAll([
            "Describe this image",
            "Extract text from image",
            "Analyze image content",
          ]);
          break;
      }
    }

    // 上下文相关操作
    if (context != null && context.isNotEmpty) {
      if (context.contains('error') || context.contains('bug')) {
        actions.addAll([
          "Help debug this issue",
          "Suggest fixes",
          "Explain the error",
        ]);
      }

      if (context.contains('?') || context.contains('how')) {
        actions.addAll([
          "Provide step-by-step guide",
          "Show examples",
          "Explain in detail",
        ]);
      }
    }

    // 随机排序并返回前8个
    actions.shuffle(_random);
    return actions.take(8).toList();
  }

  /// 构建聊天上下文
  String _buildChatContext(List<MessageModel>? messages, String? currentInput) {
    if (messages == null || messages.isEmpty) {
      return currentInput ?? '';
    }

    // 获取最近的几条消息作为上下文
    final recentMessages = messages.reversed.take(5).toList().reversed;
    final context = StringBuffer();

    for (final message in recentMessages) {
      context.writeln('${message.role}: ${message.content}');
    }

    if (currentInput != null && currentInput.isNotEmpty) {
      context.writeln('user_input: $currentInput');
    }

    return context.toString();
  }

  /// 生成上下文相关建议
  Future<List<String>> _generateContextualSuggestions({
    required String context,
    required String type,
    required int maxCount,
  }) async {
    // 占位符实现 - 实际应该调用LLM服务
    await Future.delayed(Duration(milliseconds: 300));

    final suggestions = <String>[];

    // 分析上下文关键词
    final keywords = _extractKeywords(context);

    // 基于关键词生成建议
    for (final keyword in keywords.take(maxCount)) {
      switch (type) {
        case 'chat':
          suggestions.add(_generateChatSuggestionForKeyword(keyword));
          break;
        case 'document':
          suggestions.add(_generateDocumentSuggestionForKeyword(keyword));
          break;
      }
    }

    // 如果建议不够，补充默认建议
    while (suggestions.length < maxCount) {
      final defaultSuggestions = type == 'chat'
          ? _getDefaultChatSuggestions(maxCount)
          : _getDefaultDocumentSuggestions(
              'text',
              maxCount,
            ).map((s) => s.description).toList();

      for (final suggestion in defaultSuggestions) {
        if (!suggestions.contains(suggestion)) {
          suggestions.add(suggestion);
          if (suggestions.length >= maxCount) break;
        }
      }
      break;
    }

    return suggestions.take(maxCount).toList();
  }

  /// 提取关键词
  List<String> _extractKeywords(String text) {
    if (text.isEmpty) return [];

    // 简单关键词提取（实际应该使用更复杂的NLP算法）
    final words = text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(' ')
        .where((word) => word.length > 3)
        .toSet()
        .toList();

    // 过滤常用词
    final stopWords = {
      'this',
      'that',
      'with',
      'have',
      'will',
      'from',
      'they',
      'been',
      'said',
      'each',
      'which',
      'their',
    };
    words.removeWhere((word) => stopWords.contains(word));

    return words.take(10).toList();
  }

  /// 为关键词生成聊天建议
  String _generateChatSuggestionForKeyword(String keyword) {
    final templates = [
      "Tell me more about $keyword",
      "How does $keyword work?",
      "What are the benefits of $keyword?",
      "Can you explain $keyword in detail?",
      "Show me examples of $keyword",
      "What are common problems with $keyword?",
    ];

    return templates[_random.nextInt(templates.length)];
  }

  /// 为关键词生成文档建议
  String _generateDocumentSuggestionForKeyword(String keyword) {
    final templates = [
      "Add more details about $keyword",
      "Improve the $keyword section",
      "Explain $keyword better",
      "Add examples for $keyword",
      "Clarify the $keyword concept",
    ];

    return templates[_random.nextInt(templates.length)];
  }

  /// 生成文本编辑建议
  Future<List<SuggestionModel>> _generateTextSuggestions(
    DocumentModel document,
    String? selectedText,
    int? selectionStart,
    int? selectionEnd,
  ) async {
    final suggestions = <SuggestionModel>[];
    final uuid = 'suggestion_${DateTime.now().millisecondsSinceEpoch}';

    // 基础编辑建议
    suggestions.addAll([
      SuggestionModel(
        id: '${uuid}_1',
        documentId: document.id,
        description: 'Improve grammar and readability',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_2',
        documentId: document.id,
        description: 'Make the text more concise',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_3',
        documentId: document.id,
        description: 'Add more detailed explanations',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
    ]);

    // 如果有选中文本，添加特定建议
    if (selectedText != null && selectedText.isNotEmpty) {
      suggestions.add(
        SuggestionModel(
          id: '${uuid}_4',
          documentId: document.id,
          description: 'Rephrase selected text',
          selectionStart: selectionStart,
          selectionEnd: selectionEnd,
          createdAt: DateTime.now(),
          isApplied: false,
        ),
      );
    }

    return suggestions;
  }

  /// 生成代码编辑建议
  Future<List<SuggestionModel>> _generateCodeSuggestions(
    DocumentModel document,
    String? selectedText,
    int? selectionStart,
    int? selectionEnd,
  ) async {
    final suggestions = <SuggestionModel>[];
    final uuid = 'suggestion_${DateTime.now().millisecondsSinceEpoch}';

    suggestions.addAll([
      SuggestionModel(
        id: '${uuid}_1',
        documentId: document.id,
        description: 'Add code comments',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_2',
        documentId: document.id,
        description: 'Optimize performance',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_3',
        documentId: document.id,
        description: 'Refactor for better readability',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_4',
        documentId: document.id,
        description: 'Add error handling',
        selectionStart: selectionStart,
        selectionEnd: selectionEnd,
        createdAt: DateTime.now(),
        isApplied: false,
      ),
    ]);

    return suggestions;
  }

  /// 生成图片编辑建议
  Future<List<SuggestionModel>> _generateImageSuggestions(
    DocumentModel document,
  ) async {
    final suggestions = <SuggestionModel>[];
    final uuid = 'suggestion_${DateTime.now().millisecondsSinceEpoch}';

    suggestions.addAll([
      SuggestionModel(
        id: '${uuid}_1',
        documentId: document.id,
        description: 'Adjust brightness and contrast',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_2',
        documentId: document.id,
        description: 'Apply color filters',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_3',
        documentId: document.id,
        description: 'Crop and resize',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
    ]);

    return suggestions;
  }

  /// 生成表格编辑建议
  Future<List<SuggestionModel>> _generateSheetSuggestions(
    DocumentModel document,
  ) async {
    final suggestions = <SuggestionModel>[];
    final uuid = 'suggestion_${DateTime.now().millisecondsSinceEpoch}';

    suggestions.addAll([
      SuggestionModel(
        id: '${uuid}_1',
        documentId: document.id,
        description: 'Add column headers',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_2',
        documentId: document.id,
        description: 'Format numbers',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
      SuggestionModel(
        id: '${uuid}_3',
        documentId: document.id,
        description: 'Add calculations',
        createdAt: DateTime.now(),
        isApplied: false,
      ),
    ]);

    return suggestions;
  }

  /// 获取默认聊天建议
  List<String> _getDefaultChatSuggestions(int count) {
    final defaults = [
      "How can I help you today?",
      "What would you like to know?",
      "Can you tell me more about your question?",
      "What specific information are you looking for?",
      "How can I assist with your project?",
      "What topic interests you most?",
    ];

    defaults.shuffle(_random);
    return defaults.take(count).toList();
  }

  /// 获取默认文档建议
  List<SuggestionModel> _getDefaultDocumentSuggestions(String kind, int count) {
    final uuid = 'default_${DateTime.now().millisecondsSinceEpoch}';
    final suggestions = <SuggestionModel>[];

    final descriptions = {
      'text': ['Improve readability', 'Add more details', 'Fix grammar'],
      'code': ['Add comments', 'Optimize code', 'Handle errors'],
      'image': ['Adjust colors', 'Resize image', 'Apply filters'],
      'sheet': ['Format data', 'Add formulas', 'Create charts'],
    };

    final typeDescriptions = descriptions[kind] ?? descriptions['text']!;

    for (int i = 0; i < count && i < typeDescriptions.length; i++) {
      suggestions.add(
        SuggestionModel(
          id: '${uuid}_$i',
          documentId: '',
          description: typeDescriptions[i],
          createdAt: DateTime.now(),
          isApplied: false,
        ),
      );
    }

    return suggestions;
  }
}
