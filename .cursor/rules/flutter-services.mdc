# Flutter 服务层设计规则

## 服务层架构

### 1. 服务分层
```
services/
├── database_service.dart    # 数据库服务
├── llm_service.dart        # LLM API服务
├── file_service.dart       # 文件处理服务
├── weather_service.dart    # 天气API服务
└── suggestion_service.dart # 建议系统服务
```

### 2. 服务实例化
```dart
// ❌ 禁止依赖注入
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db;
  ChatViewModel(this._db);  // 构造函数注入
}

// ✅ 正确做法
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();  // 直接实例化
  final LLMService _llm = LLMService();
}
```

## 核心服务设计

### 1. DatabaseService
```dart
class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final path = await getDatabasesPath();
    final dbPath = join(path, 'chatbot.db');
    
    return await openDatabase(
      dbPath,
      version: 1,
      onCreate: _createTables,
    );
  }
  
  // 核心CRUD操作
  Future<void> insertChat(ChatModel chat) async { ... }
  Future<List<ChatModel>> getAllChats() async { ... }
  Future<void> updateChat(ChatModel chat) async { ... }
  Future<void> deleteChat(String chatId) async { ... }
}
```

### 2. LLMService (API占位)
```dart
class LLMService {
  // TODO: 实际LLM API调用
  Future<String> generateResponse(String prompt, List<AttachmentModel> attachments) async {
    // 模拟API延迟
    await Future.delayed(Duration(seconds: 1));
    
    // 占位符响应
    return "This is a placeholder response. Replace with actual LLM API call.";
  }
  
  // TODO: 流式响应
  Stream<String> generateStreamResponse(String prompt, List<AttachmentModel> attachments) async* {
    final response = await generateResponse(prompt, attachments);
    
    // 模拟流式输出
    for (int i = 0; i < response.length; i++) {
      yield response.substring(0, i + 1);
      await Future.delayed(Duration(milliseconds: 50));
    }
  }
  
  // TODO: 多模态分析
  Future<String> analyzeImage(String imagePath) async {
    await Future.delayed(Duration(seconds: 2));
    return "Image analysis result placeholder";
  }
}
```

### 3. FileService
```dart
class FileService {
  // 文件上传
  Future<String> uploadFile(File file) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final newPath = '${directory.path}/$fileName';
      
      await file.copy(newPath);
      return newPath;
    } catch (e) {
      throw FileException('Failed to upload file: $e');
    }
  }
  
  // 文件删除
  Future<void> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw FileException('Failed to delete file: $e');
    }
  }
  
  // 文件压缩
  Future<File> compressImage(File imageFile) async {
    // 实现图片压缩逻辑
    return imageFile;
  }
}
```

## 错误处理规范

### 1. 自定义异常
```dart
class DatabaseException implements Exception {
  final String message;
  DatabaseException(this.message);
  
  @override
  String toString() => 'DatabaseException: $message';
}

class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}

class FileException implements Exception {
  final String message;
  FileException(this.message);
  
  @override
  String toString() => 'FileException: $message';
}
```

### 2. 统一错误处理
```dart
class ErrorHandler {
  static void handleError(dynamic error) {
    if (error is DatabaseException) {
      _showError('数据库错误: ${error.message}');
    } else if (error is NetworkException) {
      _showError('网络错误: ${error.message}');
    } else if (error is FileException) {
      _showError('文件错误: ${error.message}');
    } else {
      _showError('未知错误: ${error.toString()}');
    }
  }
  
  static void _showError(String message) {
    // 显示错误消息给用户
    print('Error: $message');
  }
}
```

## 网络请求管理

### 1. HTTP客户端配置
```dart
class HttpService {
  static final Dio _dio = Dio();
  
  static void initialize() {
    _dio.options.baseUrl = 'https://api.example.com';
    _dio.options.connectTimeout = Duration(seconds: 5);
    _dio.options.receiveTimeout = Duration(seconds: 3);
    
    // 添加拦截器
    _dio.interceptors.add(LogInterceptor());
    _dio.interceptors.add(RetryInterceptor());
  }
  
  static Future<Response> get(String path) async {
    try {
      return await _dio.get(path);
    } catch (e) {
      throw NetworkException('GET request failed: $e');
    }
  }
  
  static Future<Response> post(String path, dynamic data) async {
    try {
      return await _dio.post(path, data: data);
    } catch (e) {
      throw NetworkException('POST request failed: $e');
    }
  }
}
```

### 2. 重试机制
```dart
class RetryInterceptor extends Interceptor {
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 429 || err.response?.statusCode == 500) {
      // 实现重试逻辑
      _retryRequest(err, handler);
    } else {
      handler.next(err);
    }
  }
  
  void _retryRequest(DioError err, ErrorInterceptorHandler handler) {
    // 重试逻辑实现
  }
}
```

## 缓存策略

### 1. 内存缓存
```dart
class CacheService {
  static final Map<String, dynamic> _cache = {};
  
  static void set(String key, dynamic value) {
    _cache[key] = value;
  }
  
  static T? get<T>(String key) {
    return _cache[key] as T?;
  }
  
  static void remove(String key) {
    _cache.remove(key);
  }
  
  static void clear() {
    _cache.clear();
  }
}
```

### 2. 持久化缓存
```dart
class PersistentCacheService {
  static SharedPreferences? _prefs;
  
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  static Future<void> setString(String key, String value) async {
    await _prefs?.setString(key, value);
  }
  
  static String? getString(String key) {
    return _prefs?.getString(key);
  }
  
  static Future<void> remove(String key) async {
    await _prefs?.remove(key);
  }
}
```

## 日志管理

### 1. 日志服务
```dart
class LogService {
  static void debug(String message) {
    if (kDebugMode) {
      print('[DEBUG] $message');
    }
  }
  
  static void info(String message) {
    print('[INFO] $message');
  }
  
  static void error(String message, [dynamic error]) {
    print('[ERROR] $message');
    if (error != null) {
      print('[ERROR] $error');
    }
  }
  
  static void warning(String message) {
    print('[WARNING] $message');
  }
}
```

### 2. 性能监控
```dart
class PerformanceService {
  static final Map<String, Stopwatch> _timers = {};
  
  static void startTimer(String key) {
    _timers[key] = Stopwatch()..start();
  }
  
  static void stopTimer(String key) {
    final timer = _timers[key];
    if (timer != null) {
      timer.stop();
      LogService.info('$key took ${timer.elapsedMilliseconds}ms');
      _timers.remove(key);
    }
  }
}
```

## 配置管理

### 1. 环境配置
```dart
class ConfigService {
  static const String _environment = String.fromEnvironment('ENV', defaultValue: 'development');
  
  static bool get isDevelopment => _environment == 'development';
  static bool get isProduction => _environment == 'production';
  
  static String get apiBaseUrl {
    switch (_environment) {
      case 'production':
        return 'https://api.production.com';
      case 'staging':
        return 'https://api.staging.com';
      default:
        return 'https://api.development.com';
    }
  }
}
```

### 2. 功能开关
```dart
class FeatureFlags {
  static const bool enableArtifacts = true;
  static const bool enableVoting = true;
  static const bool enableFileUpload = true;
  static const bool enableWeatherTool = false;  // API占位
}
```

遵循这些服务层设计规则，确保创建出高质量、可维护、高性能的Flutter应用服务架构。
description:
globs:
alwaysApply: false
---
