# Flutter 数据库操作规则

## SQLite3 数据库设计

### 核心表结构
严格按照设计文档中的数据库架构实现：

```sql
-- 聊天表
CREATE TABLE chats (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  visibility TEXT DEFAULT 'private' CHECK(visibility IN ('private', 'public')),
  model_id TEXT DEFAULT 'gpt-4',
  metadata TEXT -- JSON格式存储额外信息
);

-- 消息表
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK(role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  message_type TEXT DEFAULT 'text',
  metadata TEXT,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
);

-- 其他表：attachments, votes, documents, suggestions, settings
```

## 数据库服务规范

### 1. 单例模式
```dart
class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
}
```

### 2. 原生SQL操作
- **禁止使用ORM框架**（如Drift、Floor等）
- 直接使用`sqflite`包的原生SQL
- 手动编写所有SQL语句

### 3. 错误处理
- 使用try-catch包装所有数据库操作
- 统一的错误处理机制
- 记录详细的错误日志

## 数据模型规范

### 1. 模型类设计
```dart
class MessageModel {
  final String id;
  final String chatId;
  final String role;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String messageType;
  final Map<String, dynamic>? metadata;
  
  // 构造函数
  MessageModel({...});
  
  // JSON序列化
  Map<String, dynamic> toJson() => {...};
  factory MessageModel.fromJson(Map<String, dynamic> json) => MessageModel(...);
}
```

### 2. 数据验证
- 在模型类中添加数据验证
- 确保数据类型和约束正确
- 处理空值和默认值

## 性能优化

### 1. 数据库索引
- 为常用查询字段建立索引
- 特别是外键和时间戳字段

### 2. 批量操作
- 使用事务处理批量插入/更新
- 减少数据库连接次数

### 3. 分页查询
- 对大量数据使用LIMIT和OFFSET
- 实现懒加载机制
description:
globs:
alwaysApply: false
---
