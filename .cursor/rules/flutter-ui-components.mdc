# Flutter UI组件设计规则

## 组件架构原则

### 1. 组件分层
```
views/
├── pages/           # 页面级组件
├── components/      # 通用组件
├── dialogs/         # 对话框组件
└── ui/             # 基础UI组件
```

### 2. 状态管理集成
```dart
class ChatPage extends StatefulWidget {
  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late ChatViewModel _viewModel;
  
  @override
  void initState() {
    super.initState();
    _viewModel = ChatViewModel();  // 直接实例化
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierBuilder<ChatViewModel>(
      notifier: _viewModel,
      builder: (context, vm, child) {
        return Scaffold(
          // UI实现
        );
      },
    );
  }
  
  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }
}
```

## 核心组件设计

### 1. 聊天相关组件
- **ChatPage**: 主聊天页面
- **MessageList**: 消息列表
- **MessageItem**: 单条消息
- **MessageActions**: 消息操作按钮
- **MultimodalInput**: 多模态输入框

### 2. 侧边栏组件
- **AppSidebar**: 应用侧边栏
- **ChatHistoryList**: 聊天历史列表
- **ChatHistoryItem**: 单个聊天历史项
- **SidebarToggle**: 侧边栏切换按钮

### 3. Artifact组件
- **ArtifactPage**: Artifact页面
- **TextEditor**: 文本编辑器
- **CodeEditor**: 代码编辑器
- **ImageEditor**: 图片编辑器
- **SheetEditor**: 表格编辑器

## 响应式设计

### 1. 屏幕适配
```dart
class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget tablet;
  final Widget desktop;
  
  const ResponsiveBuilder({
    required this.mobile,
    required this.tablet,
    required this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          return mobile;
        } else if (constraints.maxWidth < 1200) {
          return tablet;
        } else {
          return desktop;
        }
      },
    );
  }
}
```

### 2. 断点管理
- **Mobile**: < 600px
- **Tablet**: 600px - 1200px
- **Desktop**: > 1200px

## 主题和样式

### 1. 主题集成
```dart
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  
  const CustomButton({
    required this.text,
    required this.onPressed,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
      child: Text(text),
    );
  }
}
```

### 2. 颜色系统
- 遵循Material Design 3色彩系统
- 支持深色/浅色主题切换
- 一致的色彩应用

## 性能优化

### 1. 列表优化
```dart
class MessageList extends StatelessWidget {
  final List<MessageModel> messages;
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      // 懒加载
      itemCount: messages.length,
      itemBuilder: (context, index) {
        return MessageItem(
          key: ValueKey(messages[index].id),  // 稳定的key
          message: messages[index],
        );
      },
    );
  }
}
```

### 2. 图片优化
- 使用`CachedNetworkImage`缓存网络图片
- 实现图片懒加载
- 压缩和缩放图片

### 3. 动画优化
- 使用`AnimatedBuilder`精确控制动画
- 避免过度的动画效果
- 合理使用`RepaintBoundary`

## 用户体验

### 1. 加载状态
```dart
class LoadingIndicator extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  
  const LoadingIndicator({
    required this.isLoading,
    required this.child,
  });
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black26,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }
}
```

### 2. 错误处理
- 统一的错误显示组件
- 用户友好的错误信息
- 重试机制

### 3. 交互反馈
- 按钮点击反馈
- 长按操作提示
- 滑动操作指示

## 可访问性

### 1. 语义化标签
```dart
Semantics(
  label: 'Send message',
  child: IconButton(
    icon: Icon(Icons.send),
    onPressed: onSend,
  ),
)
```

### 2. 键盘导航
- 支持Tab键导航
- 合理的焦点顺序
- 快捷键支持

### 3. 屏幕阅读器
- 提供清晰的语音描述
- 重要信息的朗读
- 状态变化提示

## 组件复用

### 1. 通用组件
- 创建可配置的通用组件
- 统一的组件接口
- 良好的组件文档

### 2. 组件组合
- 使用组合而非继承
- 小而专一的组件
- 清晰的组件边界

遵循这些UI组件设计规则，确保创建出高质量、高性能、用户友好的Flutter应用界面。
description:
globs:
alwaysApply: false
---
