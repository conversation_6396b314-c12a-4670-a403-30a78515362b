---
alwaysApply: true
---

# Flutter AI Chatbot 完整功能设计文档

## 核心原则

基于TypeScript原项目**完全复刻**，严格遵循以下要求：
1. **SQLite3**：原生数据库操作，无ORM
2. **无依赖注入**：直接实例化，无Provider/GetIt
3. **MVVM架构**：ViewModel直接持有，组件化组合
4. **无授权系统**：移除所有认证相关功能
5. **LLM API占位**：网络请求接口预留，功能完全本地实现

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── models/                      # 数据模型
│   ├── message_model.dart
│   ├── chat_model.dart
│   ├── attachment_model.dart
│   ├── artifact_model.dart
│   ├── vote_model.dart
│   ├── suggestion_model.dart
│   └── document_model.dart
├── viewmodels/                  # 视图模型
│   ├── chat_viewmodel.dart
│   ├── chat_list_viewmodel.dart
│   ├── artifact_viewmodel.dart
│   ├── message_viewmodel.dart
│   ├── model_selector_viewmodel.dart
│   └── visibility_viewmodel.dart
├── views/                       # 视图层
│   ├── chat_page.dart
│   ├── artifact_page.dart
│   ├── sidebar/
│   │   ├── app_sidebar.dart
│   │   ├── chat_history_list.dart
│   │   └── chat_history_item.dart
│   ├── components/
│   │   ├── chat_header.dart
│   │   ├── multimodal_input.dart
│   │   ├── message_list.dart
│   │   ├── message_item.dart
│   │   ├── message_actions.dart
│   │   ├── suggested_actions.dart
│   │   ├── model_selector.dart
│   │   ├── visibility_selector.dart
│   │   ├── attachment_preview.dart
│   │   ├── artifact_components/
│   │   │   ├── text_editor.dart
│   │   │   ├── code_editor.dart
│   │   │   ├── image_editor.dart
│   │   │   └── sheet_editor.dart
│   │   └── ui/
│   │       ├── custom_button.dart
│   │       ├── custom_input.dart
│   │       ├── custom_dropdown.dart
│   │       └── loading_indicator.dart
│   └── dialogs/
│       ├── delete_confirm_dialog.dart
│       └── error_dialog.dart
├── services/                    # 服务层
│   ├── database_service.dart
│   ├── llm_service.dart
│   ├── file_service.dart
│   ├── weather_service.dart
│   └── suggestion_service.dart
├── utils/                       # 工具类
│   ├── date_utils.dart
│   ├── string_utils.dart
│   ├── file_utils.dart
│   └── constants.dart
└── themes/                      # 主题管理
    ├── app_theme.dart
    ├── light_theme.dart
    └── dark_theme.dart
```

## 完整功能清单

### 1. 侧边栏和聊天历史 ✅
- **分组显示**：今天、昨天、最近7天、最近30天、更早
- **无限滚动**：分页加载历史记录
- **实时同步**：当前活跃聊天高亮显示
- **聊天管理**：删除、重命名、可见性切换
- **搜索过滤**：按标题搜索聊天记录

### 2. 聊天核心功能 ✅
- **实时对话**：流式响应显示
- **消息类型**：文本、图片、文件、工具调用
- **消息操作**：复制、编辑、重新生成、删除
- **投票系统**：点赞/点踩功能
- **消息历史**：完整的对话记录

### 3. 多模态输入系统 ✅
- **文本输入**：支持多行、快捷键
- **文件上传**：图片、文档、多种格式
- **附件预览**：实时预览上传文件
- **建议操作**：预设问题快速选择
- **滚动控制**：自动滚动到底部

### 4. AI模型管理 ✅
- **模型选择器**：支持多种AI模型
- **模型切换**：实时切换不同模型
- **模型信息**：显示模型描述和能力
- **设置保存**：记住用户选择

### 5. 可见性控制 ✅
- **私有聊天**：仅本地访问
- **公共聊天**：可分享链接（本地模拟）
- **动态切换**：聊天过程中可修改
- **状态同步**：实时更新可见性状态

### 6. Artifacts系统 ✅
- **文本编辑器**：富文本编辑，支持Markdown
- **代码编辑器**：语法高亮，多语言支持
- **图片编辑器**：基础图片处理功能
- **表格编辑器**：电子表格功能
- **版本控制**：多版本管理
- **实时保存**：自动保存修改

### 7. 建议系统 ✅
- **智能建议**：基于上下文生成建议
- **操作建议**：快速操作推荐
- **内容建议**：编辑器内智能提示
- **自定义建议**：用户可添加常用操作

### 8. 工具集成 ✅
- **天气查询**：获取天气信息（API占位）
- **文档处理**：创建、更新、搜索文档
- **建议生成**：AI生成操作建议
- **文件处理**：上传、预览、下载

### 9. 投票和反馈系统 ✅
- **消息投票**：点赞/点踩AI回复
- **投票状态**：显示投票结果
- **反馈收集**：用户反馈机制
- **质量评估**：对话质量评分

### 10. 主题和UI系统 ✅
- **深色/浅色模式**：完整主题切换
- **响应式设计**：适配不同屏幕尺寸
- **动画效果**：平滑过渡动画
- **自定义主题**：用户可调整主题色彩

### 11. 错误处理和状态管理 ✅
- **统一错误处理**：全局错误捕获
- **状态指示**：加载、错误、成功状态
- **重试机制**：网络请求重试
- **离线支持**：本地数据缓存

### 12. 性能优化 ✅
- **懒加载**：按需加载组件
- **虚拟列表**：大量数据优化
- **图片优化**：压缩、缓存
- **内存管理**：及时释放资源

## 数据库设计（SQLite3）

### 核心表结构

```sql
-- 聊天表
CREATE TABLE chats (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  visibility TEXT DEFAULT 'private' CHECK(visibility IN ('private', 'public')),
  model_id TEXT DEFAULT 'gpt-4',
  metadata TEXT -- JSON格式存储额外信息
);

-- 消息表
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK(role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  message_type TEXT DEFAULT 'text',
  metadata TEXT, -- JSON格式存储附加数据
  FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
);

-- 附件表
CREATE TABLE attachments (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 投票表
CREATE TABLE votes (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  is_upvoted BOOLEAN NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 文档表（Artifacts）
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  kind TEXT NOT NULL CHECK(kind IN ('text', 'code', 'image', 'sheet')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  version INTEGER DEFAULT 1,
  parent_id TEXT, -- 用于版本管理
  metadata TEXT -- JSON格式存储编辑器特定数据
);

-- 建议表
CREATE TABLE suggestions (
  id TEXT PRIMARY KEY,
  document_id TEXT NOT NULL,
  description TEXT NOT NULL,
  selection_start INTEGER,
  selection_end INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_applied BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- 设置表
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 核心ViewModel设计

### ChatViewModel
```dart
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();
  final LLMService _llm = LLMService();
  
  List<MessageModel> _messages = [];
  bool _isLoading = false;
  String _selectedModel = 'gpt-4';
  String _visibility = 'private';
  
  List<MessageModel> get messages => _messages;
  bool get isLoading => _isLoading;
  String get selectedModel => _selectedModel;
  String get visibility => _visibility;
  
  // 发送消息
  Future<void> sendMessage(String content, List<AttachmentModel> attachments) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      // 保存用户消息
      final userMessage = MessageModel(
        id: generateId(),
        chatId: currentChatId,
        role: 'user',
        content: content,
        createdAt: DateTime.now(),
      );
      await _db.insertMessage(userMessage);
      _messages.add(userMessage);
      
      // 调用LLM API（占位）
      final response = await _llm.generateResponse(content, attachments);
      
      // 保存AI回复
      final aiMessage = MessageModel(
        id: generateId(),
        chatId: currentChatId,
        role: 'assistant',
        content: response,
        createdAt: DateTime.now(),
      );
      await _db.insertMessage(aiMessage);
      _messages.add(aiMessage);
      
    } catch (e) {
      // 错误处理
      showError(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // 投票功能
  Future<void> voteMessage(String messageId, bool isUpvoted) async {
    try {
      await _db.upsertVote(messageId, isUpvoted);
      notifyListeners();
    } catch (e) {
      showError(e.toString());
    }
  }
  
  // 切换模型
  void selectModel(String modelId) {
    _selectedModel = modelId;
    notifyListeners();
  }
  
  // 切换可见性
  Future<void> setVisibility(String vis) async {
    _visibility = vis;
    await _db.updateChatVisibility(currentChatId, vis);
    notifyListeners();
  }
}
```

### ChatListViewModel
```dart
class ChatListViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();
  
  List<ChatModel> _chats = [];
  bool _isLoading = false;
  String _searchQuery = '';
  
  List<ChatModel> get chats => _getFilteredChats();
  bool get isLoading => _isLoading;
  
  // 分组显示聊天
  Map<String, List<ChatModel>> get groupedChats {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final lastWeek = today.subtract(Duration(days: 7));
    final lastMonth = today.subtract(Duration(days: 30));
    
    return {
      'Today': _chats.where((chat) => chat.createdAt.isAfter(today)).toList(),
      'Yesterday': _chats.where((chat) => 
          chat.createdAt.isAfter(yesterday) && 
          chat.createdAt.isBefore(today)).toList(),
      'Last 7 days': _chats.where((chat) => 
          chat.createdAt.isAfter(lastWeek) && 
          chat.createdAt.isBefore(yesterday)).toList(),
      'Last 30 days': _chats.where((chat) => 
          chat.createdAt.isAfter(lastMonth) && 
          chat.createdAt.isBefore(lastWeek)).toList(),
      'Older': _chats.where((chat) => 
          chat.createdAt.isBefore(lastMonth)).toList(),
    };
  }
  
  // 加载聊天列表
  Future<void> loadChats() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _chats = await _db.getAllChats();
    } catch (e) {
      showError(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // 删除聊天
  Future<void> deleteChat(String chatId) async {
    try {
      await _db.deleteChat(chatId);
      _chats.removeWhere((chat) => chat.id == chatId);
      notifyListeners();
    } catch (e) {
      showError(e.toString());
    }
  }
  
  // 搜索过滤
  List<ChatModel> _getFilteredChats() {
    if (_searchQuery.isEmpty) return _chats;
    return _chats.where((chat) => 
        chat.title.toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }
  
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
}
```

## 服务层设计

### DatabaseService
```dart
class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final path = await getDatabasesPath();
    final dbPath = join(path, 'chatbot.db');
    
    return await openDatabase(
      dbPath,
      version: 1,
      onCreate: _createTables,
    );
  }
  
  Future<void> _createTables(Database db, int version) async {
    // 创建所有表
    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        visibility TEXT DEFAULT 'private',
        model_id TEXT DEFAULT 'gpt-4',
        metadata TEXT
      )
    ''');
    
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        chat_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        message_type TEXT DEFAULT 'text',
        metadata TEXT,
        FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
      )
    ''');
    
    // 其他表...
  }
  
  // 聊天操作
  Future<void> insertChat(ChatModel chat) async {
    final db = await database;
    await db.insert('chats', chat.toJson());
  }
  
  Future<List<ChatModel>> getAllChats() async {
    final db = await database;
    final result = await db.query('chats', orderBy: 'updated_at DESC');
    return result.map((json) => ChatModel.fromJson(json)).toList();
  }
  
  Future<void> deleteChat(String chatId) async {
    final db = await database;
    await db.delete('chats', where: 'id = ?', whereArgs: [chatId]);
  }
  
  // 消息操作
  Future<void> insertMessage(MessageModel message) async {
    final db = await database;
    await db.insert('messages', message.toJson());
  }
  
  Future<List<MessageModel>> getMessagesByChat(String chatId) async {
    final db = await database;
    final result = await db.query(
      'messages', 
      where: 'chat_id = ?', 
      whereArgs: [chatId],
      orderBy: 'created_at ASC'
    );
    return result.map((json) => MessageModel.fromJson(json)).toList();
  }
  
  // 投票操作
  Future<void> upsertVote(String messageId, bool isUpvoted) async {
    final db = await database;
    await db.insert(
      'votes',
      {
        'id': generateId(),
        'message_id': messageId,
        'is_upvoted': isUpvoted,
        'created_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
  
  Future<Map<String, VoteModel>> getVotesByChat(String chatId) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT v.* FROM votes v
      JOIN messages m ON v.message_id = m.id
      WHERE m.chat_id = ?
    ''', [chatId]);
    
    final votes = <String, VoteModel>{};
    for (final json in result) {
      final vote = VoteModel.fromJson(json);
      votes[vote.messageId] = vote;
    }
    return votes;
  }
}
```

### LLMService
```dart
class LLMService {
  // TODO: 实际LLM API调用
  Future<String> generateResponse(String prompt, List<AttachmentModel> attachments) async {
    // 模拟流式响应
    await Future.delayed(Duration(seconds: 1));
    
    // 这里应该是实际的API调用
    return "This is a placeholder response. Replace with actual LLM API call.";
  }
  
  // TODO: 流式响应
  Stream<String> generateStreamResponse(String prompt, List<AttachmentModel> attachments) async* {
    final response = await generateResponse(prompt, attachments);
    
    // 模拟流式输出
    for (int i = 0; i < response.length; i++) {
      yield response.substring(0, i + 1);
      await Future.delayed(Duration(milliseconds: 50));
    }
  }
  
  // TODO: 图片分析
  Future<String> analyzeImage(String imagePath) async {
    await Future.delayed(Duration(seconds: 2));
    return "Image analysis result placeholder";
  }
  
  // TODO: 文档处理
  Future<String> processDocument(String documentPath) async {
    await Future.delayed(Duration(seconds: 3));
    return "Document processing result placeholder";
  }
}
```

## 主要组件设计

### ChatPage
```dart
class ChatPage extends StatefulWidget {
  final String chatId;
  
  const ChatPage({Key? key, required this.chatId}) : super(key: key);
  
  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late ChatViewModel _viewModel;
  
  @override
  void initState() {
    super.initState();
    _viewModel = ChatViewModel();
    _viewModel.loadChat(widget.chatId);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatHeader(viewModel: _viewModel),
      body: Column(
        children: [
          Expanded(
            child: MessageList(viewModel: _viewModel),
          ),
          MultimodalInput(viewModel: _viewModel),
        ],
      ),
    );
  }
}
```

### MessageList
```dart
class MessageList extends StatelessWidget {
  final ChatViewModel viewModel;
  
  const MessageList({Key? key, required this.viewModel}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierBuilder<ChatViewModel>(
      notifier: viewModel,
      builder: (context, vm, child) {
        if (vm.isLoading && vm.messages.isEmpty) {
          return Center(child: CircularProgressIndicator());
        }
        
        return ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: vm.messages.length,
          itemBuilder: (context, index) {
            final message = vm.messages[index];
            return MessageItem(
              message: message,
              onVote: vm.voteMessage,
              onEdit: vm.editMessage,
              onRegenerate: vm.regenerateMessage,
            );
          },
        );
      },
    );
  }
}
```

### MessageItem
```dart
class MessageItem extends StatelessWidget {
  final MessageModel message;
  final Function(String, bool) onVote;
  final Function(String, String) onEdit;
  final Function(String) onRegenerate;
  
  const MessageItem({
    Key? key,
    required this.message,
    required this.onVote,
    required this.onEdit,
    required this.onRegenerate,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: message.role == 'user' ? Colors.blue.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 消息内容
          MarkdownBody(data: message.content),
          
          // 消息操作
          if (message.role == 'assistant') ...[
            SizedBox(height: 8),
            MessageActions(
              messageId: message.id,
              onVote: onVote,
              onEdit: onEdit,
              onRegenerate: onRegenerate,
            ),
          ],
        ],
      ),
    );
  }
}
```

## 主题管理

### AppTheme
```dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      // 其他主题设置...
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.grey.shade900,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      // 其他主题设置...
    );
  }
}
```

## 错误处理

### ErrorHandler
```dart
class ErrorHandler {
  static void handleError(dynamic error) {
    if (error is DatabaseException) {
      showSnackBar('Database error: ${error.toString()}');
    } else if (error is NetworkException) {
      showSnackBar('Network error: ${error.toString()}');
    } else {
      showSnackBar('Unknown error: ${error.toString()}');
    }
  }
  
  static void showSnackBar(String message) {
    // 显示错误消息
    ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
```

## 性能优化策略

### 1. 列表优化
- 使用`ListView.builder`进行懒加载
- 实现虚拟滚动减少内存占用
- 分页加载历史数据

### 2. 图片优化
- 图片压缩和缓存
- 使用`CachedNetworkImage`
- 延迟加载图片

### 3. 状态管理优化
- 使用`ChangeNotifierBuilder`精确重建
- 避免不必要的`notifyListeners()`
- 合理使用`memo`化

### 4. 数据库优化
- 建立必要的索引
- 使用事务处理批量操作
- 定期清理过期数据

## 开发阶段

### 阶段1：基础架构
- [x] 项目结构搭建
- [x] 数据库设计和实现
- [x] 基础ViewModel架构
- [x] 路由和导航

### 阶段2：核心功能
- [x] 聊天界面实现
- [x] 消息收发功能
- [x] 侧边栏历史记录
- [x] 多模态输入

### 阶段3：高级功能
- [x] Artifacts系统
- [x] 投票和反馈
- [x] 模型选择器
- [x] 可见性控制

### 阶段4：优化和完善
- [x] 主题管理
- [x] 错误处理
- [x] 性能优化
- [x] 测试和调试

## 总结

这个Flutter设计完全复刻了TypeScript原项目的所有功能，包括：

1. **完整的聊天系统**：侧边栏、历史记录、分组显示
2. **多模态交互**：文本、图片、文件上传
3. **AI功能**：模型选择、可见性控制、投票系统
4. **Artifacts系统**：4种编辑器，版本控制
5. **用户体验**：建议操作、主题管理、错误处理
6. **性能优化**：虚拟列表、图片优化、状态管理

通过严格的MVVM架构、无依赖注入的设计，以及原生SQLite3的数据管理，确保了应用的简洁性和高性能。 # Flutter AI Chatbot 完整功能设计文档

## 核心原则

基于TypeScript原项目**完全复刻**，严格遵循以下要求：
1. **SQLite3**：原生数据库操作，无ORM
2. **无依赖注入**：直接实例化，无Provider/GetIt
3. **MVVM架构**：ViewModel直接持有，组件化组合
4. **无授权系统**：移除所有认证相关功能
5. **LLM API占位**：网络请求接口预留，功能完全本地实现

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── models/                      # 数据模型
│   ├── message_model.dart
│   ├── chat_model.dart
│   ├── attachment_model.dart
│   ├── artifact_model.dart
│   ├── vote_model.dart
│   ├── suggestion_model.dart
│   └── document_model.dart
├── viewmodels/                  # 视图模型
│   ├── chat_viewmodel.dart
│   ├── chat_list_viewmodel.dart
│   ├── artifact_viewmodel.dart
│   ├── message_viewmodel.dart
│   ├── model_selector_viewmodel.dart
│   └── visibility_viewmodel.dart
├── views/                       # 视图层
│   ├── chat_page.dart
│   ├── artifact_page.dart
│   ├── sidebar/
│   │   ├── app_sidebar.dart
│   │   ├── chat_history_list.dart
│   │   └── chat_history_item.dart
│   ├── components/
│   │   ├── chat_header.dart
│   │   ├── multimodal_input.dart
│   │   ├── message_list.dart
│   │   ├── message_item.dart
│   │   ├── message_actions.dart
│   │   ├── suggested_actions.dart
│   │   ├── model_selector.dart
│   │   ├── visibility_selector.dart
│   │   ├── attachment_preview.dart
│   │   ├── artifact_components/
│   │   │   ├── text_editor.dart
│   │   │   ├── code_editor.dart
│   │   │   ├── image_editor.dart
│   │   │   └── sheet_editor.dart
│   │   └── ui/
│   │       ├── custom_button.dart
│   │       ├── custom_input.dart
│   │       ├── custom_dropdown.dart
│   │       └── loading_indicator.dart
│   └── dialogs/
│       ├── delete_confirm_dialog.dart
│       └── error_dialog.dart
├── services/                    # 服务层
│   ├── database_service.dart
│   ├── llm_service.dart
│   ├── file_service.dart
│   ├── weather_service.dart
│   └── suggestion_service.dart
├── utils/                       # 工具类
│   ├── date_utils.dart
│   ├── string_utils.dart
│   ├── file_utils.dart
│   └── constants.dart
└── themes/                      # 主题管理
    ├── app_theme.dart
    ├── light_theme.dart
    └── dark_theme.dart
```

## 完整功能清单

### 1. 侧边栏和聊天历史 ✅
- **分组显示**：今天、昨天、最近7天、最近30天、更早
- **无限滚动**：分页加载历史记录
- **实时同步**：当前活跃聊天高亮显示
- **聊天管理**：删除、重命名、可见性切换
- **搜索过滤**：按标题搜索聊天记录

### 2. 聊天核心功能 ✅
- **实时对话**：流式响应显示
- **消息类型**：文本、图片、文件、工具调用
- **消息操作**：复制、编辑、重新生成、删除
- **投票系统**：点赞/点踩功能
- **消息历史**：完整的对话记录

### 3. 多模态输入系统 ✅
- **文本输入**：支持多行、快捷键
- **文件上传**：图片、文档、多种格式
- **附件预览**：实时预览上传文件
- **建议操作**：预设问题快速选择
- **滚动控制**：自动滚动到底部

### 4. AI模型管理 ✅
- **模型选择器**：支持多种AI模型
- **模型切换**：实时切换不同模型
- **模型信息**：显示模型描述和能力
- **设置保存**：记住用户选择

### 5. 可见性控制 ✅
- **私有聊天**：仅本地访问
- **公共聊天**：可分享链接（本地模拟）
- **动态切换**：聊天过程中可修改
- **状态同步**：实时更新可见性状态

### 6. Artifacts系统 ✅
- **文本编辑器**：富文本编辑，支持Markdown
- **代码编辑器**：语法高亮，多语言支持
- **图片编辑器**：基础图片处理功能
- **表格编辑器**：电子表格功能
- **版本控制**：多版本管理
- **实时保存**：自动保存修改

### 7. 建议系统 ✅
- **智能建议**：基于上下文生成建议
- **操作建议**：快速操作推荐
- **内容建议**：编辑器内智能提示
- **自定义建议**：用户可添加常用操作

### 8. 工具集成 ✅
- **天气查询**：获取天气信息（API占位）
- **文档处理**：创建、更新、搜索文档
- **建议生成**：AI生成操作建议
- **文件处理**：上传、预览、下载

### 9. 投票和反馈系统 ✅
- **消息投票**：点赞/点踩AI回复
- **投票状态**：显示投票结果
- **反馈收集**：用户反馈机制
- **质量评估**：对话质量评分

### 10. 主题和UI系统 ✅
- **深色/浅色模式**：完整主题切换
- **响应式设计**：适配不同屏幕尺寸
- **动画效果**：平滑过渡动画
- **自定义主题**：用户可调整主题色彩

### 11. 错误处理和状态管理 ✅
- **统一错误处理**：全局错误捕获
- **状态指示**：加载、错误、成功状态
- **重试机制**：网络请求重试
- **离线支持**：本地数据缓存

### 12. 性能优化 ✅
- **懒加载**：按需加载组件
- **虚拟列表**：大量数据优化
- **图片优化**：压缩、缓存
- **内存管理**：及时释放资源

## 数据库设计（SQLite3）

### 核心表结构

```sql
-- 聊天表
CREATE TABLE chats (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  visibility TEXT DEFAULT 'private' CHECK(visibility IN ('private', 'public')),
  model_id TEXT DEFAULT 'gpt-4',
  metadata TEXT -- JSON格式存储额外信息
);

-- 消息表
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK(role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  message_type TEXT DEFAULT 'text',
  metadata TEXT, -- JSON格式存储附加数据
  FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
);

-- 附件表
CREATE TABLE attachments (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 投票表
CREATE TABLE votes (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  is_upvoted BOOLEAN NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- 文档表（Artifacts）
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  kind TEXT NOT NULL CHECK(kind IN ('text', 'code', 'image', 'sheet')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  version INTEGER DEFAULT 1,
  parent_id TEXT, -- 用于版本管理
  metadata TEXT -- JSON格式存储编辑器特定数据
);

-- 建议表
CREATE TABLE suggestions (
  id TEXT PRIMARY KEY,
  document_id TEXT NOT NULL,
  description TEXT NOT NULL,
  selection_start INTEGER,
  selection_end INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_applied BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- 设置表
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 核心ViewModel设计

### ChatViewModel
```dart
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();
  final LLMService _llm = LLMService();
  
  List<MessageModel> _messages = [];
  bool _isLoading = false;
  String _selectedModel = 'gpt-4';
  String _visibility = 'private';
  
  List<MessageModel> get messages => _messages;
  bool get isLoading => _isLoading;
  String get selectedModel => _selectedModel;
  String get visibility => _visibility;
  
  // 发送消息
  Future<void> sendMessage(String content, List<AttachmentModel> attachments) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      // 保存用户消息
      final userMessage = MessageModel(
        id: generateId(),
        chatId: currentChatId,
        role: 'user',
        content: content,
        createdAt: DateTime.now(),
      );
      await _db.insertMessage(userMessage);
      _messages.add(userMessage);
      
      // 调用LLM API（占位）
      final response = await _llm.generateResponse(content, attachments);
      
      // 保存AI回复
      final aiMessage = MessageModel(
        id: generateId(),
        chatId: currentChatId,
        role: 'assistant',
        content: response,
        createdAt: DateTime.now(),
      );
      await _db.insertMessage(aiMessage);
      _messages.add(aiMessage);
      
    } catch (e) {
      // 错误处理
      showError(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // 投票功能
  Future<void> voteMessage(String messageId, bool isUpvoted) async {
    try {
      await _db.upsertVote(messageId, isUpvoted);
      notifyListeners();
    } catch (e) {
      showError(e.toString());
    }
  }
  
  // 切换模型
  void selectModel(String modelId) {
    _selectedModel = modelId;
    notifyListeners();
  }
  
  // 切换可见性
  Future<void> setVisibility(String vis) async {
    _visibility = vis;
    await _db.updateChatVisibility(currentChatId, vis);
    notifyListeners();
  }
}
```

### ChatListViewModel
```dart
class ChatListViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();
  
  List<ChatModel> _chats = [];
  bool _isLoading = false;
  String _searchQuery = '';
  
  List<ChatModel> get chats => _getFilteredChats();
  bool get isLoading => _isLoading;
  
  // 分组显示聊天
  Map<String, List<ChatModel>> get groupedChats {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final lastWeek = today.subtract(Duration(days: 7));
    final lastMonth = today.subtract(Duration(days: 30));
    
    return {
      'Today': _chats.where((chat) => chat.createdAt.isAfter(today)).toList(),
      'Yesterday': _chats.where((chat) => 
          chat.createdAt.isAfter(yesterday) && 
          chat.createdAt.isBefore(today)).toList(),
      'Last 7 days': _chats.where((chat) => 
          chat.createdAt.isAfter(lastWeek) && 
          chat.createdAt.isBefore(yesterday)).toList(),
      'Last 30 days': _chats.where((chat) => 
          chat.createdAt.isAfter(lastMonth) && 
          chat.createdAt.isBefore(lastWeek)).toList(),
      'Older': _chats.where((chat) => 
          chat.createdAt.isBefore(lastMonth)).toList(),
    };
  }
  
  // 加载聊天列表
  Future<void> loadChats() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _chats = await _db.getAllChats();
    } catch (e) {
      showError(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // 删除聊天
  Future<void> deleteChat(String chatId) async {
    try {
      await _db.deleteChat(chatId);
      _chats.removeWhere((chat) => chat.id == chatId);
      notifyListeners();
    } catch (e) {
      showError(e.toString());
    }
  }
  
  // 搜索过滤
  List<ChatModel> _getFilteredChats() {
    if (_searchQuery.isEmpty) return _chats;
    return _chats.where((chat) => 
        chat.title.toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }
  
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
}
```

## 服务层设计

### DatabaseService
```dart
class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final path = await getDatabasesPath();
    final dbPath = join(path, 'chatbot.db');
    
    return await openDatabase(
      dbPath,
      version: 1,
      onCreate: _createTables,
    );
  }
  
  Future<void> _createTables(Database db, int version) async {
    // 创建所有表
    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        visibility TEXT DEFAULT 'private',
        model_id TEXT DEFAULT 'gpt-4',
        metadata TEXT
      )
    ''');
    
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        chat_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        message_type TEXT DEFAULT 'text',
        metadata TEXT,
        FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
      )
    ''');
    
    // 其他表...
  }
  
  // 聊天操作
  Future<void> insertChat(ChatModel chat) async {
    final db = await database;
    await db.insert('chats', chat.toJson());
  }
  
  Future<List<ChatModel>> getAllChats() async {
    final db = await database;
    final result = await db.query('chats', orderBy: 'updated_at DESC');
    return result.map((json) => ChatModel.fromJson(json)).toList();
  }
  
  Future<void> deleteChat(String chatId) async {
    final db = await database;
    await db.delete('chats', where: 'id = ?', whereArgs: [chatId]);
  }
  
  // 消息操作
  Future<void> insertMessage(MessageModel message) async {
    final db = await database;
    await db.insert('messages', message.toJson());
  }
  
  Future<List<MessageModel>> getMessagesByChat(String chatId) async {
    final db = await database;
    final result = await db.query(
      'messages', 
      where: 'chat_id = ?', 
      whereArgs: [chatId],
      orderBy: 'created_at ASC'
    );
    return result.map((json) => MessageModel.fromJson(json)).toList();
  }
  
  // 投票操作
  Future<void> upsertVote(String messageId, bool isUpvoted) async {
    final db = await database;
    await db.insert(
      'votes',
      {
        'id': generateId(),
        'message_id': messageId,
        'is_upvoted': isUpvoted,
        'created_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
  
  Future<Map<String, VoteModel>> getVotesByChat(String chatId) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT v.* FROM votes v
      JOIN messages m ON v.message_id = m.id
      WHERE m.chat_id = ?
    ''', [chatId]);
    
    final votes = <String, VoteModel>{};
    for (final json in result) {
      final vote = VoteModel.fromJson(json);
      votes[vote.messageId] = vote;
    }
    return votes;
  }
}
```

### LLMService
```dart
class LLMService {
  // TODO: 实际LLM API调用
  Future<String> generateResponse(String prompt, List<AttachmentModel> attachments) async {
    // 模拟流式响应
    await Future.delayed(Duration(seconds: 1));
    
    // 这里应该是实际的API调用
    return "This is a placeholder response. Replace with actual LLM API call.";
  }
  
  // TODO: 流式响应
  Stream<String> generateStreamResponse(String prompt, List<AttachmentModel> attachments) async* {
    final response = await generateResponse(prompt, attachments);
    
    // 模拟流式输出
    for (int i = 0; i < response.length; i++) {
      yield response.substring(0, i + 1);
      await Future.delayed(Duration(milliseconds: 50));
    }
  }
  
  // TODO: 图片分析
  Future<String> analyzeImage(String imagePath) async {
    await Future.delayed(Duration(seconds: 2));
    return "Image analysis result placeholder";
  }
  
  // TODO: 文档处理
  Future<String> processDocument(String documentPath) async {
    await Future.delayed(Duration(seconds: 3));
    return "Document processing result placeholder";
  }
}
```

## 主要组件设计

### ChatPage
```dart
class ChatPage extends StatefulWidget {
  final String chatId;
  
  const ChatPage({Key? key, required this.chatId}) : super(key: key);
  
  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late ChatViewModel _viewModel;
  
  @override
  void initState() {
    super.initState();
    _viewModel = ChatViewModel();
    _viewModel.loadChat(widget.chatId);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatHeader(viewModel: _viewModel),
      body: Column(
        children: [
          Expanded(
            child: MessageList(viewModel: _viewModel),
          ),
          MultimodalInput(viewModel: _viewModel),
        ],
      ),
    );
  }
}
```

### MessageList
```dart
class MessageList extends StatelessWidget {
  final ChatViewModel viewModel;
  
  const MessageList({Key? key, required this.viewModel}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierBuilder<ChatViewModel>(
      notifier: viewModel,
      builder: (context, vm, child) {
        if (vm.isLoading && vm.messages.isEmpty) {
          return Center(child: CircularProgressIndicator());
        }
        
        return ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: vm.messages.length,
          itemBuilder: (context, index) {
            final message = vm.messages[index];
            return MessageItem(
              message: message,
              onVote: vm.voteMessage,
              onEdit: vm.editMessage,
              onRegenerate: vm.regenerateMessage,
            );
          },
        );
      },
    );
  }
}
```

### MessageItem
```dart
class MessageItem extends StatelessWidget {
  final MessageModel message;
  final Function(String, bool) onVote;
  final Function(String, String) onEdit;
  final Function(String) onRegenerate;
  
  const MessageItem({
    Key? key,
    required this.message,
    required this.onVote,
    required this.onEdit,
    required this.onRegenerate,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: message.role == 'user' ? Colors.blue.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 消息内容
          MarkdownBody(data: message.content),
          
          // 消息操作
          if (message.role == 'assistant') ...[
            SizedBox(height: 8),
            MessageActions(
              messageId: message.id,
              onVote: onVote,
              onEdit: onEdit,
              onRegenerate: onRegenerate,
            ),
          ],
        ],
      ),
    );
  }
}
```

## 主题管理

### AppTheme
```dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      // 其他主题设置...
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.grey.shade900,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      // 其他主题设置...
    );
  }
}
```

## 错误处理

### ErrorHandler
```dart
class ErrorHandler {
  static void handleError(dynamic error) {
    if (error is DatabaseException) {
      showSnackBar('Database error: ${error.toString()}');
    } else if (error is NetworkException) {
      showSnackBar('Network error: ${error.toString()}');
    } else {
      showSnackBar('Unknown error: ${error.toString()}');
    }
  }
  
  static void showSnackBar(String message) {
    // 显示错误消息
    ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
```

## 性能优化策略

### 1. 列表优化
- 使用`ListView.builder`进行懒加载
- 实现虚拟滚动减少内存占用
- 分页加载历史数据

### 2. 图片优化
- 图片压缩和缓存
- 使用`CachedNetworkImage`
- 延迟加载图片

### 3. 状态管理优化
- 使用`ChangeNotifierBuilder`精确重建
- 避免不必要的`notifyListeners()`
- 合理使用`memo`化

### 4. 数据库优化
- 建立必要的索引
- 使用事务处理批量操作
- 定期清理过期数据

## 开发阶段

### 阶段1：基础架构
- [x] 项目结构搭建
- [x] 数据库设计和实现
- [x] 基础ViewModel架构
- [x] 路由和导航

### 阶段2：核心功能
- [x] 聊天界面实现
- [x] 消息收发功能
- [x] 侧边栏历史记录
- [x] 多模态输入

### 阶段3：高级功能
- [x] Artifacts系统
- [x] 投票和反馈
- [x] 模型选择器
- [x] 可见性控制

### 阶段4：优化和完善
- [x] 主题管理
- [x] 错误处理
- [x] 性能优化
- [x] 测试和调试

## 总结

这个Flutter设计完全复刻了TypeScript原项目的所有功能，包括：

1. **完整的聊天系统**：侧边栏、历史记录、分组显示
2. **多模态交互**：文本、图片、文件上传
3. **AI功能**：模型选择、可见性控制、投票系统
4. **Artifacts系统**：4种编辑器，版本控制
5. **用户体验**：建议操作、主题管理、错误处理
6. **性能优化**：虚拟列表、图片优化、状态管理

通过严格的MVVM架构、无依赖注入的设计，以及原生SQLite3的数据管理，确保了应用的简洁性和高性能。 