# Flutter ViewModel 设计规则

## ViewModel基础架构

### 1. 继承ChangeNotifier
```dart
class ChatViewModel extends ChangeNotifier {
  // 服务层直接实例化
  final DatabaseService _db = DatabaseService();
  final LLMService _llm = LLMService();
  
  // 私有状态变量
  List<MessageModel> _messages = [];
  bool _isLoading = false;
  String _selectedModel = 'gpt-4';
  
  // 公开getter
  List<MessageModel> get messages => _messages;
  bool get isLoading => _isLoading;
  String get selectedModel => _selectedModel;
  
  // 业务方法
  Future<void> sendMessage(String content) async {
    // 实现逻辑
  }
}
```

### 2. 状态管理原则
- **私有状态变量**：使用下划线前缀
- **公开getter**：提供只读访问
- **显式通知**：在适当时机调用`notifyListeners()`
- **异步处理**：使用Future/async-await

## 核心ViewModel设计

### 1. ChatViewModel
负责单个聊天会话的状态管理：
- 消息列表管理
- 发送消息功能
- 模型选择
- 可见性控制
- 投票功能

### 2. ChatListViewModel
负责聊天列表的状态管理：
- 聊天历史加载
- 分组显示（今天、昨天、最近7天等）
- 搜索过滤
- 聊天删除

### 3. ArtifactViewModel
负责Artifacts系统：
- 文档管理
- 版本控制
- 编辑器状态
- 建议系统

## 错误处理规范

### 1. 统一错误处理
```dart
Future<void> loadMessages() async {
  _isLoading = true;
  notifyListeners();
  
  try {
    _messages = await _db.getMessagesByChat(chatId);
  } catch (e) {
    _handleError(e);
  } finally {
    _isLoading = false;
    notifyListeners();
  }
}

void _handleError(dynamic error) {
  // 统一错误处理逻辑
  ErrorHandler.handleError(error);
}
```

### 2. 加载状态管理
- 使用`_isLoading`标识异步操作状态
- 在操作开始时设置为true
- 在finally块中恢复状态

## 性能优化

### 1. 精确重建
- 仅在必要时调用`notifyListeners()`
- 避免频繁的状态更新
- 使用debounce处理快速连续操作

### 2. 内存管理
- 在dispose中释放资源
- 避免内存泄漏
- 及时清理监听器

### 3. 数据缓存
- 合理使用缓存减少数据库查询
- 实现数据的延迟加载
- 优化列表渲染性能

## 禁止使用的模式

### 1. 依赖注入框架
```dart
// ❌ 禁止使用
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db;
  ChatViewModel(this._db);  // 构造函数注入
}

// ✅ 正确做法
class ChatViewModel extends ChangeNotifier {
  final DatabaseService _db = DatabaseService();  // 直接实例化
}
```

### 2. 复杂的状态管理
- 禁止使用Provider、Riverpod、BLoC等复杂状态管理
- 禁止使用GetX等第三方状态管理框架
- 保持简单的ChangeNotifier模式

## 数据流设计

### 1. 单向数据流
- View -> ViewModel -> Service -> Database
- 数据变化通过`notifyListeners()`传播
- 避免循环依赖

### 2. 组件通信
- 通过ViewModel暴露的方法进行操作
- 使用回调函数处理组件间通信
- 避免直接的组件间依赖
description:
globs:
alwaysApply: false
---
